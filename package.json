{"name": "comp6452-project2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check .", "format:fix": "prettier --write --list-different ."}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@rainbow-me/rainbowkit": "^2.2.7", "@tanstack/react-query": "^5.80.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.515.0", "next": "15.3.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "tailwind-merge": "^3.3.1", "wagmi": "^2.15.6", "zod": "^3.25.64"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@nomicfoundation/hardhat-ignition": "^0.15.11", "@nomicfoundation/hardhat-ignition-viem": "^0.15.11", "@nomicfoundation/hardhat-network-helpers": "^1.0.12", "@nomicfoundation/hardhat-toolbox": "^5.0.0", "@nomicfoundation/hardhat-toolbox-viem": "^3.0.0", "@nomicfoundation/hardhat-verify": "^2.0.14", "@nomicfoundation/hardhat-viem": "^2.0.6", "@tailwindcss/postcss": "^4.1.10", "@types/chai": "^4.3.20", "@types/chai-as-promised": "^7.1.8", "@types/mocha": "^10.0.10", "@types/node": "^20.19.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "chai": "^4.5.0", "dotenv": "^16.5.0", "eslint": "^9.29.0", "eslint-config-next": "15.3.3", "hardhat": "^2.24.3", "hardhat-gas-reporter": "^1.0.10", "prettier": "^3.5.3", "solidity-coverage": "^0.8.16", "tailwindcss": "^4.1.10", "ts-node": "^10.9.2", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3", "viem": "^2.31.2"}, "packageManager": "pnpm@9.15.1+sha512.1acb565e6193efbebda772702950469150cf12bcc764262e7587e71d19dc98a423dff9536e57ea44c49bdf790ff694e83c27be5faa23d67e0c033b583be4bfcf"}