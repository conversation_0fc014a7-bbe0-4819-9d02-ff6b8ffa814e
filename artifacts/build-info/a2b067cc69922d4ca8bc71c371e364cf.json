{"id": "a2b067cc69922d4ca8bc71c371e364cf", "_format": "hh-sol-build-info-1", "solcVersion": "0.8.19", "solcLongVersion": "0.8.19+commit.7dd6d404", "input": {"language": "Solidity", "sources": {"contracts/SimpleVoting.sol": {"content": "// SPDX-License-Identifier: MIT\npragma solidity ^0.8.19;\n\n/**\n * @title SimpleVoting\n * @dev A simple voting contract for creating polls and casting votes\n */\ncontract SimpleVoting {\n    struct Poll {\n        string question;\n        string[] options;\n        uint256[] votes;\n        mapping(address => bool) hasVoted;\n        mapping(address => uint256) voterChoice;\n        bool isActive;\n        address creator;\n        uint256 createdAt;\n    }\n\n    Poll[] public polls;\n    uint256 public pollCount;\n    \n    // Optional: mapping from creator to their poll IDs\n    mapping(address => uint256[]) public creatorPolls;\n\n    // Events\n    event PollCreated(\n        uint256 indexed pollId,\n        address indexed creator,\n        string question,\n        uint256 optionCount\n    );\n\n    event VoteCast(\n        uint256 indexed pollId,\n        address indexed voter,\n        uint256 optionIndex\n    );\n\n    event PollStatusChanged(\n        uint256 indexed pollId,\n        bool isActive\n    );\n\n    // Modifiers\n    modifier pollExists(uint256 _pollId) {\n        require(_pollId < polls.length, \"Poll does not exist\");\n        _;\n    }\n\n    modifier pollActive(uint256 _pollId) {\n        require(polls[_pollId].isActive, \"Poll is not active\");\n        _;\n    }\n\n    modifier hasNotVoted(uint256 _pollId) {\n        require(!polls[_pollId].hasVoted[msg.sender], \"Already voted\");\n        _;\n    }\n\n    modifier validOption(uint256 _pollId, uint256 _optionIndex) {\n        require(_optionIndex < polls[_pollId].options.length, \"Invalid option\");\n        _;\n    }\n\n    modifier validPollData(string memory _question, string[] memory _options) {\n        require(bytes(_question).length > 0, \"Question cannot be empty\");\n        require(_options.length >= 2, \"At least 2 options required\");\n        require(_options.length <= 10, \"Too many options\");\n\n        for (uint i = 0; i < _options.length; i++) {\n            require(bytes(_options[i]).length > 0, \"Option cannot be empty\");\n        }\n        _;\n    }\n\n    /**\n     * @dev Create a new poll\n     * @param _question The poll question\n     * @param _options Array of voting options\n     * @return pollId The ID of the created poll\n     */\n    function createPoll(\n        string memory _question,\n        string[] memory _options\n    ) external validPollData(_question, _options) returns (uint256 pollId) {\n        pollId = polls.length;\n        \n        // Create new poll\n        polls.push();\n        Poll storage newPoll = polls[pollId];\n        \n        newPoll.question = _question;\n        newPoll.options = _options;\n        newPoll.votes = new uint256[](_options.length);\n        newPoll.isActive = true;\n        newPoll.creator = msg.sender;\n        newPoll.createdAt = block.timestamp;\n        \n        // Update mappings\n        pollCount++;\n        creatorPolls[msg.sender].push(pollId);\n        \n        emit PollCreated(pollId, msg.sender, _question, _options.length);\n    }\n\n    /**\n     * @dev Cast a vote on a poll\n     * @param _pollId The poll ID\n     * @param _optionIndex The index of the chosen option\n     */\n    function vote(\n        uint256 _pollId,\n        uint256 _optionIndex\n    ) \n        external \n        pollExists(_pollId) \n        pollActive(_pollId) \n        hasNotVoted(_pollId) \n        validOption(_pollId, _optionIndex) \n    {\n        Poll storage poll = polls[_pollId];\n        \n        poll.hasVoted[msg.sender] = true;\n        poll.voterChoice[msg.sender] = _optionIndex;\n        poll.votes[_optionIndex]++;\n        \n        emit VoteCast(_pollId, msg.sender, _optionIndex);\n    }\n\n    /**\n     * @dev Get poll information\n     * @param _pollId The poll ID\n     * @return question The poll question\n     * @return options Array of voting options\n     * @return votes Array of vote counts for each option\n     * @return isActive Whether the poll is active\n     * @return creator The poll creator address\n     * @return createdAt The poll creation timestamp\n     */\n    function getPoll(uint256 _pollId)\n        external\n        view\n        pollExists(_pollId)\n        returns (\n            string memory question,\n            string[] memory options,\n            uint256[] memory votes,\n            bool isActive,\n            address creator,\n            uint256 createdAt\n        )\n    {\n        Poll storage poll = polls[_pollId];\n        return (\n            poll.question,\n            poll.options,\n            poll.votes,\n            poll.isActive,\n            poll.creator,\n            poll.createdAt\n        );\n    }\n\n    /**\n     * @dev Get the total number of polls\n     * @return The total poll count\n     */\n    function getPollCount() external view returns (uint256) {\n        return polls.length;\n    }\n\n    /**\n     * @dev Check if a user has voted on a poll\n     * @param _pollId The poll ID\n     * @param _user The user address\n     * @return Whether the user has voted\n     */\n    function hasUserVoted(\n        uint256 _pollId,\n        address _user\n    ) external view pollExists(_pollId) returns (bool) {\n        return polls[_pollId].hasVoted[_user];\n    }\n\n    /**\n     * @dev Get a user's vote choice\n     * @param _pollId The poll ID\n     * @param _user The user address\n     * @return The user's vote choice (option index)\n     */\n    function getUserVote(\n        uint256 _pollId,\n        address _user\n    ) external view pollExists(_pollId) returns (uint256) {\n        require(polls[_pollId].hasVoted[_user], \"User has not voted\");\n        return polls[_pollId].voterChoice[_user];\n    }\n\n    /**\n     * @dev Toggle poll active status (only creator can call)\n     * @param _pollId The poll ID\n     */\n    function togglePollStatus(uint256 _pollId) \n        external \n        pollExists(_pollId) \n    {\n        require(polls[_pollId].creator == msg.sender, \"Only creator can toggle status\");\n        \n        polls[_pollId].isActive = !polls[_pollId].isActive;\n        emit PollStatusChanged(_pollId, polls[_pollId].isActive);\n    }\n\n    /**\n     * @dev Get polls created by a specific address\n     * @param _creator The creator address\n     * @return Array of poll IDs created by the address\n     */\n    function getCreatorPolls(address _creator) external view returns (uint256[] memory) {\n        return creatorPolls[_creator];\n    }\n}\n"}}, "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "output": {"sources": {"contracts/SimpleVoting.sol": {"ast": {"absolutePath": "contracts/SimpleVoting.sol", "exportedSymbols": {"SimpleVoting": [506]}, "id": 507, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 1, "literals": ["solidity", "^", "0.8", ".19"], "nodeType": "PragmaDirective", "src": "32:24:0"}, {"abstract": false, "baseContracts": [], "canonicalName": "SimpleVoting", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 2, "nodeType": "StructuredDocumentation", "src": "58:100:0", "text": " @title SimpleVoting\n @dev A simple voting contract for creating polls and casting votes"}, "fullyImplemented": true, "id": 506, "linearizedBaseContracts": [506], "name": "SimpleVoting", "nameLocation": "168:12:0", "nodeType": "ContractDefinition", "nodes": [{"canonicalName": "SimpleVoting.Poll", "id": 25, "members": [{"constant": false, "id": 4, "mutability": "mutable", "name": "question", "nameLocation": "216:8:0", "nodeType": "VariableDeclaration", "scope": 25, "src": "209:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}, "typeName": {"id": 3, "name": "string", "nodeType": "ElementaryTypeName", "src": "209:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7, "mutability": "mutable", "name": "options", "nameLocation": "243:7:0", "nodeType": "VariableDeclaration", "scope": 25, "src": "234:16:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage_ptr", "typeString": "string[]"}, "typeName": {"baseType": {"id": 5, "name": "string", "nodeType": "ElementaryTypeName", "src": "234:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "id": 6, "nodeType": "ArrayTypeName", "src": "234:8:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage_ptr", "typeString": "string[]"}}, "visibility": "internal"}, {"constant": false, "id": 10, "mutability": "mutable", "name": "votes", "nameLocation": "270:5:0", "nodeType": "VariableDeclaration", "scope": 25, "src": "260:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}, "typeName": {"baseType": {"id": 8, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "260:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 9, "nodeType": "ArrayTypeName", "src": "260:9:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}, "visibility": "internal"}, {"constant": false, "id": 14, "mutability": "mutable", "name": "hasVoted", "nameLocation": "310:8:0", "nodeType": "VariableDeclaration", "scope": 25, "src": "285:33:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}, "typeName": {"id": 13, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 11, "name": "address", "nodeType": "ElementaryTypeName", "src": "293:7:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "285:24:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 12, "name": "bool", "nodeType": "ElementaryTypeName", "src": "304:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}}, "visibility": "internal"}, {"constant": false, "id": 18, "mutability": "mutable", "name": "voterChoice", "nameLocation": "356:11:0", "nodeType": "VariableDeclaration", "scope": 25, "src": "328:39:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "typeName": {"id": 17, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 15, "name": "address", "nodeType": "ElementaryTypeName", "src": "336:7:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "328:27:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 16, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "347:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}, "visibility": "internal"}, {"constant": false, "id": 20, "mutability": "mutable", "name": "isActive", "nameLocation": "382:8:0", "nodeType": "VariableDeclaration", "scope": 25, "src": "377:13:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 19, "name": "bool", "nodeType": "ElementaryTypeName", "src": "377:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 22, "mutability": "mutable", "name": "creator", "nameLocation": "408:7:0", "nodeType": "VariableDeclaration", "scope": 25, "src": "400:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 21, "name": "address", "nodeType": "ElementaryTypeName", "src": "400:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 24, "mutability": "mutable", "name": "createdAt", "nameLocation": "433:9:0", "nodeType": "VariableDeclaration", "scope": 25, "src": "425:17:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 23, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "425:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "name": "Poll", "nameLocation": "194:4:0", "nodeType": "StructDefinition", "scope": 506, "src": "187:262:0", "visibility": "public"}, {"constant": false, "functionSelector": "ac2f0074", "id": 29, "mutability": "mutable", "name": "polls", "nameLocation": "469:5:0", "nodeType": "VariableDeclaration", "scope": 506, "src": "455:19:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Poll_$25_storage_$dyn_storage", "typeString": "struct SimpleVoting.Poll[]"}, "typeName": {"baseType": {"id": 27, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 26, "name": "Poll", "nameLocations": ["455:4:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 25, "src": "455:4:0"}, "referencedDeclaration": 25, "src": "455:4:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage_ptr", "typeString": "struct SimpleVoting.Poll"}}, "id": 28, "nodeType": "ArrayTypeName", "src": "455:6:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Poll_$25_storage_$dyn_storage_ptr", "typeString": "struct SimpleVoting.Poll[]"}}, "visibility": "public"}, {"constant": false, "functionSelector": "9207891d", "id": 31, "mutability": "mutable", "name": "pollCount", "nameLocation": "495:9:0", "nodeType": "VariableDeclaration", "scope": 506, "src": "480:24:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 30, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "480:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "public"}, {"constant": false, "functionSelector": "8439e976", "id": 36, "mutability": "mutable", "name": "creatorP<PERSON>s", "nameLocation": "608:12:0", "nodeType": "VariableDeclaration", "scope": 506, "src": "571:49:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_array$_t_uint256_$dyn_storage_$", "typeString": "mapping(address => uint256[])"}, "typeName": {"id": 35, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 32, "name": "address", "nodeType": "ElementaryTypeName", "src": "579:7:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "571:29:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_array$_t_uint256_$dyn_storage_$", "typeString": "mapping(address => uint256[])"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"baseType": {"id": 33, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "590:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 34, "nodeType": "ArrayTypeName", "src": "590:9:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}}, "visibility": "public"}, {"anonymous": false, "eventSelector": "9dbafd4116dcead87280b4bad16e92c851a647fbf87a77e05cf6b29c8fcdc33c", "id": 46, "name": "PollCreated", "nameLocation": "647:11:0", "nodeType": "EventDefinition", "parameters": {"id": 45, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 38, "indexed": true, "mutability": "mutable", "name": "pollId", "nameLocation": "684:6:0", "nodeType": "VariableDeclaration", "scope": 46, "src": "668:22:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 37, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "668:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 40, "indexed": true, "mutability": "mutable", "name": "creator", "nameLocation": "716:7:0", "nodeType": "VariableDeclaration", "scope": 46, "src": "700:23:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 39, "name": "address", "nodeType": "ElementaryTypeName", "src": "700:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 42, "indexed": false, "mutability": "mutable", "name": "question", "nameLocation": "740:8:0", "nodeType": "VariableDeclaration", "scope": 46, "src": "733:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 41, "name": "string", "nodeType": "ElementaryTypeName", "src": "733:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 44, "indexed": false, "mutability": "mutable", "name": "optionCount", "nameLocation": "766:11:0", "nodeType": "VariableDeclaration", "scope": 46, "src": "758:19:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 43, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "758:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "658:125:0"}, "src": "641:143:0"}, {"anonymous": false, "eventSelector": "2acce567deca3aabf56327adbb4524bd5318936eaefa69e3a5208ffda0cfec09", "id": 54, "name": "VoteCast", "nameLocation": "796:8:0", "nodeType": "EventDefinition", "parameters": {"id": 53, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 48, "indexed": true, "mutability": "mutable", "name": "pollId", "nameLocation": "830:6:0", "nodeType": "VariableDeclaration", "scope": 54, "src": "814:22:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 47, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "814:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 50, "indexed": true, "mutability": "mutable", "name": "voter", "nameLocation": "862:5:0", "nodeType": "VariableDeclaration", "scope": 54, "src": "846:21:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 49, "name": "address", "nodeType": "ElementaryTypeName", "src": "846:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 52, "indexed": false, "mutability": "mutable", "name": "optionIndex", "nameLocation": "885:11:0", "nodeType": "VariableDeclaration", "scope": 54, "src": "877:19:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 51, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "877:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "804:98:0"}, "src": "790:113:0"}, {"anonymous": false, "eventSelector": "b7b8c0761d078843a3e84d05266ab3eca7a488cd293cce2eac1441c9287d4b53", "id": 60, "name": "PollStatusChanged", "nameLocation": "915:17:0", "nodeType": "EventDefinition", "parameters": {"id": 59, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 56, "indexed": true, "mutability": "mutable", "name": "pollId", "nameLocation": "958:6:0", "nodeType": "VariableDeclaration", "scope": 60, "src": "942:22:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 55, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "942:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 58, "indexed": false, "mutability": "mutable", "name": "isActive", "nameLocation": "979:8:0", "nodeType": "VariableDeclaration", "scope": 60, "src": "974:13:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 57, "name": "bool", "nodeType": "ElementaryTypeName", "src": "974:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "932:61:0"}, "src": "909:85:0"}, {"body": {"id": 73, "nodeType": "Block", "src": "1054:82:0", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 68, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 65, "name": "_pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 62, "src": "1072:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"expression": {"id": 66, "name": "polls", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 29, "src": "1082:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Poll_$25_storage_$dyn_storage", "typeString": "struct SimpleVoting.Poll storage ref[] storage ref"}}, "id": 67, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1088:6:0", "memberName": "length", "nodeType": "MemberAccess", "src": "1082:12:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1072:22:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "506f6c6c20646f6573206e6f74206578697374", "id": 69, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1096:21:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_a570800e348d215f0f8e7aaa1afa84c103bca1d27fa74d7cf9c25b83f991280b", "typeString": "literal_string \"Poll does not exist\""}, "value": "Poll does not exist"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_a570800e348d215f0f8e7aaa1afa84c103bca1d27fa74d7cf9c25b83f991280b", "typeString": "literal_string \"Poll does not exist\""}], "id": 64, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "1064:7:0", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 70, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1064:54:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 71, "nodeType": "ExpressionStatement", "src": "1064:54:0"}, {"id": 72, "nodeType": "PlaceholderStatement", "src": "1128:1:0"}]}, "id": 74, "name": "pollExists", "nameLocation": "1026:10:0", "nodeType": "ModifierDefinition", "parameters": {"id": 63, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 62, "mutability": "mutable", "name": "_pollId", "nameLocation": "1045:7:0", "nodeType": "VariableDeclaration", "scope": 74, "src": "1037:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 61, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1037:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1036:17:0"}, "src": "1017:119:0", "virtual": false, "visibility": "internal"}, {"body": {"id": 87, "nodeType": "Block", "src": "1179:82:0", "statements": [{"expression": {"arguments": [{"expression": {"baseExpression": {"id": 79, "name": "polls", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 29, "src": "1197:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Poll_$25_storage_$dyn_storage", "typeString": "struct SimpleVoting.Poll storage ref[] storage ref"}}, "id": 81, "indexExpression": {"id": 80, "name": "_pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 76, "src": "1203:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1197:14:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage", "typeString": "struct SimpleVoting.Poll storage ref"}}, "id": 82, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "1212:8:0", "memberName": "isActive", "nodeType": "MemberAccess", "referencedDeclaration": 20, "src": "1197:23:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "506f6c6c206973206e6f7420616374697665", "id": 83, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1222:20:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_85be9a3a8b38ddaef4e16dab673e124fe2fa73224e04bf43989f8bc735b45740", "typeString": "literal_string \"Poll is not active\""}, "value": "Poll is not active"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_85be9a3a8b38ddaef4e16dab673e124fe2fa73224e04bf43989f8bc735b45740", "typeString": "literal_string \"Poll is not active\""}], "id": 78, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "1189:7:0", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 84, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1189:54:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 85, "nodeType": "ExpressionStatement", "src": "1189:54:0"}, {"id": 86, "nodeType": "PlaceholderStatement", "src": "1253:1:0"}]}, "id": 88, "name": "pollActive", "nameLocation": "1151:10:0", "nodeType": "ModifierDefinition", "parameters": {"id": 77, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 76, "mutability": "mutable", "name": "_pollId", "nameLocation": "1170:7:0", "nodeType": "VariableDeclaration", "scope": 88, "src": "1162:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 75, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1162:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1161:17:0"}, "src": "1142:119:0", "virtual": false, "visibility": "internal"}, {"body": {"id": 105, "nodeType": "Block", "src": "1305:90:0", "statements": [{"expression": {"arguments": [{"id": 100, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "1323:36:0", "subExpression": {"baseExpression": {"expression": {"baseExpression": {"id": 93, "name": "polls", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 29, "src": "1324:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Poll_$25_storage_$dyn_storage", "typeString": "struct SimpleVoting.Poll storage ref[] storage ref"}}, "id": 95, "indexExpression": {"id": 94, "name": "_pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 90, "src": "1330:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1324:14:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage", "typeString": "struct SimpleVoting.Poll storage ref"}}, "id": 96, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "1339:8:0", "memberName": "hasVoted", "nodeType": "MemberAccess", "referencedDeclaration": 14, "src": "1324:23:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 99, "indexExpression": {"expression": {"id": 97, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "1348:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 98, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1352:6:0", "memberName": "sender", "nodeType": "MemberAccess", "src": "1348:10:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1324:35:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "416c726561647920766f746564", "id": 101, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1361:15:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_512fc59044d4f0722f9346c450973ffe8aac7aa1142e536739987018593c53b6", "typeString": "literal_string \"Already voted\""}, "value": "Already voted"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_512fc59044d4f0722f9346c450973ffe8aac7aa1142e536739987018593c53b6", "typeString": "literal_string \"Already voted\""}], "id": 92, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "1315:7:0", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 102, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1315:62:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 103, "nodeType": "ExpressionStatement", "src": "1315:62:0"}, {"id": 104, "nodeType": "PlaceholderStatement", "src": "1387:1:0"}]}, "id": 106, "name": "hasNotVoted", "nameLocation": "1276:11:0", "nodeType": "ModifierDefinition", "parameters": {"id": 91, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 90, "mutability": "mutable", "name": "_pollId", "nameLocation": "1296:7:0", "nodeType": "VariableDeclaration", "scope": 106, "src": "1288:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 89, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1288:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1287:17:0"}, "src": "1267:128:0", "virtual": false, "visibility": "internal"}, {"body": {"id": 124, "nodeType": "Block", "src": "1461:99:0", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 119, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 113, "name": "_optionIndex", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110, "src": "1479:12:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"expression": {"expression": {"baseExpression": {"id": 114, "name": "polls", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 29, "src": "1494:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Poll_$25_storage_$dyn_storage", "typeString": "struct SimpleVoting.Poll storage ref[] storage ref"}}, "id": 116, "indexExpression": {"id": 115, "name": "_pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 108, "src": "1500:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1494:14:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage", "typeString": "struct SimpleVoting.Poll storage ref"}}, "id": 117, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "1509:7:0", "memberName": "options", "nodeType": "MemberAccess", "referencedDeclaration": 7, "src": "1494:22:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage", "typeString": "string storage ref[] storage ref"}}, "id": 118, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1517:6:0", "memberName": "length", "nodeType": "MemberAccess", "src": "1494:29:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1479:44:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "496e76616c6964206f7074696f6e", "id": 120, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1525:16:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_47303e9cecb0d187ef38fc0ef78d94b0ad3bfd977aa49a6ef487d19e96bc3077", "typeString": "literal_string \"Invalid option\""}, "value": "Invalid option"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_47303e9cecb0d187ef38fc0ef78d94b0ad3bfd977aa49a6ef487d19e96bc3077", "typeString": "literal_string \"Invalid option\""}], "id": 112, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "1471:7:0", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 121, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1471:71:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 122, "nodeType": "ExpressionStatement", "src": "1471:71:0"}, {"id": 123, "nodeType": "PlaceholderStatement", "src": "1552:1:0"}]}, "id": 125, "name": "validOption", "nameLocation": "1410:11:0", "nodeType": "ModifierDefinition", "parameters": {"id": 111, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 108, "mutability": "mutable", "name": "_pollId", "nameLocation": "1430:7:0", "nodeType": "VariableDeclaration", "scope": 125, "src": "1422:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 107, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1422:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 110, "mutability": "mutable", "name": "_optionIndex", "nameLocation": "1447:12:0", "nodeType": "VariableDeclaration", "scope": 125, "src": "1439:20:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 109, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1439:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1421:39:0"}, "src": "1401:159:0", "virtual": false, "visibility": "internal"}, {"body": {"id": 186, "nodeType": "Block", "src": "1640:364:0", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 139, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"arguments": [{"id": 135, "name": "_question", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 127, "src": "1664:9:0", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 134, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1658:5:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes_storage_ptr_$", "typeString": "type(bytes storage pointer)"}, "typeName": {"id": 133, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "1658:5:0", "typeDescriptions": {}}}, "id": 136, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1658:16:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 137, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1675:6:0", "memberName": "length", "nodeType": "MemberAccess", "src": "1658:23:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 138, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1684:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "1658:27:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "5175657374696f6e2063616e6e6f7420626520656d707479", "id": 140, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1687:26:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_e91f32e8485e8cc61ee75db81581675d5614973e773b4b2d6682176c252ce441", "typeString": "literal_string \"Question cannot be empty\""}, "value": "Question cannot be empty"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_e91f32e8485e8cc61ee75db81581675d5614973e773b4b2d6682176c252ce441", "typeString": "literal_string \"Question cannot be empty\""}], "id": 132, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "1650:7:0", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 141, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1650:64:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 142, "nodeType": "ExpressionStatement", "src": "1650:64:0"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 147, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 144, "name": "_options", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 130, "src": "1732:8:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}, "id": 145, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1741:6:0", "memberName": "length", "nodeType": "MemberAccess", "src": "1732:15:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"hexValue": "32", "id": 146, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1751:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_2_by_1", "typeString": "int_const 2"}, "value": "2"}, "src": "1732:20:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4174206c656173742032206f7074696f6e73207265717569726564", "id": 148, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1754:29:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_8df94984f0986c4b0f652788cbe872845edfb9f154cbd9bb376c68bda91b6cbd", "typeString": "literal_string \"At least 2 options required\""}, "value": "At least 2 options required"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_8df94984f0986c4b0f652788cbe872845edfb9f154cbd9bb376c68bda91b6cbd", "typeString": "literal_string \"At least 2 options required\""}], "id": 143, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "1724:7:0", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 149, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1724:60:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 150, "nodeType": "ExpressionStatement", "src": "1724:60:0"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 155, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 152, "name": "_options", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 130, "src": "1802:8:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}, "id": 153, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1811:6:0", "memberName": "length", "nodeType": "MemberAccess", "src": "1802:15:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"hexValue": "3130", "id": 154, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1821:2:0", "typeDescriptions": {"typeIdentifier": "t_rational_10_by_1", "typeString": "int_const 10"}, "value": "10"}, "src": "1802:21:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "546f6f206d616e79206f7074696f6e73", "id": 156, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1825:18:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_b5c2a862288076e78ffb9f7b2c2d4242ebde1778466579712ffc2d1471c123ec", "typeString": "literal_string \"Too many options\""}, "value": "Too many options"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_b5c2a862288076e78ffb9f7b2c2d4242ebde1778466579712ffc2d1471c123ec", "typeString": "literal_string \"Too many options\""}], "id": 151, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "1794:7:0", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 157, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1794:50:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 158, "nodeType": "ExpressionStatement", "src": "1794:50:0"}, {"body": {"id": 183, "nodeType": "Block", "src": "1898:89:0", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 179, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"arguments": [{"baseExpression": {"id": 173, "name": "_options", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 130, "src": "1926:8:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}, "id": 175, "indexExpression": {"id": 174, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 160, "src": "1935:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1926:11:0", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 172, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1920:5:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes_storage_ptr_$", "typeString": "type(bytes storage pointer)"}, "typeName": {"id": 171, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "1920:5:0", "typeDescriptions": {}}}, "id": 176, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1920:18:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 177, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1939:6:0", "memberName": "length", "nodeType": "MemberAccess", "src": "1920:25:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 178, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1948:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "1920:29:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4f7074696f6e2063616e6e6f7420626520656d707479", "id": 180, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1951:24:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_e951eb8529546f8abd8d997e0753f069f65b139bcb89559e3d3f221bef3b55a5", "typeString": "literal_string \"Option cannot be empty\""}, "value": "Option cannot be empty"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_e951eb8529546f8abd8d997e0753f069f65b139bcb89559e3d3f221bef3b55a5", "typeString": "literal_string \"Option cannot be empty\""}], "id": 170, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "1912:7:0", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 181, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1912:64:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 182, "nodeType": "ExpressionStatement", "src": "1912:64:0"}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 166, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 163, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 160, "src": "1872:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"expression": {"id": 164, "name": "_options", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 130, "src": "1876:8:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}, "id": 165, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1885:6:0", "memberName": "length", "nodeType": "MemberAccess", "src": "1876:15:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1872:19:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 184, "initializationExpression": {"assignments": [160], "declarations": [{"constant": false, "id": 160, "mutability": "mutable", "name": "i", "nameLocation": "1865:1:0", "nodeType": "VariableDeclaration", "scope": 184, "src": "1860:6:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 159, "name": "uint", "nodeType": "ElementaryTypeName", "src": "1860:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 162, "initialValue": {"hexValue": "30", "id": 161, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1869:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "1860:10:0"}, "loopExpression": {"expression": {"id": 168, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "1893:3:0", "subExpression": {"id": 167, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 160, "src": "1893:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 169, "nodeType": "ExpressionStatement", "src": "1893:3:0"}, "nodeType": "ForStatement", "src": "1855:132:0"}, {"id": 185, "nodeType": "PlaceholderStatement", "src": "1996:1:0"}]}, "id": 187, "name": "validPollData", "nameLocation": "1575:13:0", "nodeType": "ModifierDefinition", "parameters": {"id": 131, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 127, "mutability": "mutable", "name": "_question", "nameLocation": "1603:9:0", "nodeType": "VariableDeclaration", "scope": 187, "src": "1589:23:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 126, "name": "string", "nodeType": "ElementaryTypeName", "src": "1589:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 130, "mutability": "mutable", "name": "_options", "nameLocation": "1630:8:0", "nodeType": "VariableDeclaration", "scope": 187, "src": "1614:24:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string[]"}, "typeName": {"baseType": {"id": 128, "name": "string", "nodeType": "ElementaryTypeName", "src": "1614:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "id": 129, "nodeType": "ArrayTypeName", "src": "1614:8:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage_ptr", "typeString": "string[]"}}, "visibility": "internal"}], "src": "1588:51:0"}, "src": "1566:438:0", "virtual": false, "visibility": "internal"}, {"body": {"id": 282, "nodeType": "Block", "src": "2356:584:0", "statements": [{"expression": {"id": 205, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 202, "name": "pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 200, "src": "2366:6:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"expression": {"id": 203, "name": "polls", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 29, "src": "2375:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Poll_$25_storage_$dyn_storage", "typeString": "struct SimpleVoting.Poll storage ref[] storage ref"}}, "id": 204, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2381:6:0", "memberName": "length", "nodeType": "MemberAccess", "src": "2375:12:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2366:21:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 206, "nodeType": "ExpressionStatement", "src": "2366:21:0"}, {"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 207, "name": "polls", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 29, "src": "2433:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Poll_$25_storage_$dyn_storage", "typeString": "struct SimpleVoting.Poll storage ref[] storage ref"}}, "id": 209, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2439:4:0", "memberName": "push", "nodeType": "MemberAccess", "src": "2433:10:0", "typeDescriptions": {"typeIdentifier": "t_function_arraypush_nonpayable$_t_array$_t_struct$_Poll_$25_storage_$dyn_storage_ptr_$returns$_t_struct$_Poll_$25_storage_$attached_to$_t_array$_t_struct$_Poll_$25_storage_$dyn_storage_ptr_$", "typeString": "function (struct SimpleVoting.Poll storage ref[] storage pointer) returns (struct SimpleVoting.Poll storage ref)"}}, "id": 210, "isConstant": false, "isLValue": true, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2433:12:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage", "typeString": "struct SimpleVoting.Poll storage ref"}}, "id": 211, "nodeType": "ExpressionStatement", "src": "2433:12:0"}, {"assignments": [214], "declarations": [{"constant": false, "id": 214, "mutability": "mutable", "name": "newPoll", "nameLocation": "2468:7:0", "nodeType": "VariableDeclaration", "scope": 282, "src": "2455:20:0", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage_ptr", "typeString": "struct SimpleVoting.Poll"}, "typeName": {"id": 213, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 212, "name": "Poll", "nameLocations": ["2455:4:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 25, "src": "2455:4:0"}, "referencedDeclaration": 25, "src": "2455:4:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage_ptr", "typeString": "struct SimpleVoting.Poll"}}, "visibility": "internal"}], "id": 218, "initialValue": {"baseExpression": {"id": 215, "name": "polls", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 29, "src": "2478:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Poll_$25_storage_$dyn_storage", "typeString": "struct SimpleVoting.Poll storage ref[] storage ref"}}, "id": 217, "indexExpression": {"id": 216, "name": "pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 200, "src": "2484:6:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2478:13:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage", "typeString": "struct SimpleVoting.Poll storage ref"}}, "nodeType": "VariableDeclarationStatement", "src": "2455:36:0"}, {"expression": {"id": 223, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 219, "name": "newPoll", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 214, "src": "2510:7:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage_ptr", "typeString": "struct SimpleVoting.Poll storage pointer"}}, "id": 221, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "2518:8:0", "memberName": "question", "nodeType": "MemberAccess", "referencedDeclaration": 4, "src": "2510:16:0", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 222, "name": "_question", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 190, "src": "2529:9:0", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "src": "2510:28:0", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "id": 224, "nodeType": "ExpressionStatement", "src": "2510:28:0"}, {"expression": {"id": 229, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 225, "name": "newPoll", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 214, "src": "2548:7:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage_ptr", "typeString": "struct SimpleVoting.Poll storage pointer"}}, "id": 227, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "2556:7:0", "memberName": "options", "nodeType": "MemberAccess", "referencedDeclaration": 7, "src": "2548:15:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage", "typeString": "string storage ref[] storage ref"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 228, "name": "_options", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 193, "src": "2566:8:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}, "src": "2548:26:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage", "typeString": "string storage ref[] storage ref"}}, "id": 230, "nodeType": "ExpressionStatement", "src": "2548:26:0"}, {"expression": {"id": 240, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 231, "name": "newPoll", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 214, "src": "2584:7:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage_ptr", "typeString": "struct SimpleVoting.Poll storage pointer"}}, "id": 233, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "2592:5:0", "memberName": "votes", "nodeType": "MemberAccess", "referencedDeclaration": 10, "src": "2584:13:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage", "typeString": "uint256[] storage ref"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"expression": {"id": 237, "name": "_options", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 193, "src": "2614:8:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}, "id": 238, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2623:6:0", "memberName": "length", "nodeType": "MemberAccess", "src": "2614:15:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 236, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "NewExpression", "src": "2600:13:0", "typeDescriptions": {"typeIdentifier": "t_function_objectcreation_pure$_t_uint256_$returns$_t_array$_t_uint256_$dyn_memory_ptr_$", "typeString": "function (uint256) pure returns (uint256[] memory)"}, "typeName": {"baseType": {"id": 234, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2604:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 235, "nodeType": "ArrayTypeName", "src": "2604:9:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}}, "id": 239, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2600:30:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "src": "2584:46:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage", "typeString": "uint256[] storage ref"}}, "id": 241, "nodeType": "ExpressionStatement", "src": "2584:46:0"}, {"expression": {"id": 246, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 242, "name": "newPoll", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 214, "src": "2640:7:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage_ptr", "typeString": "struct SimpleVoting.Poll storage pointer"}}, "id": 244, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "2648:8:0", "memberName": "isActive", "nodeType": "MemberAccess", "referencedDeclaration": 20, "src": "2640:16:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 245, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "2659:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "2640:23:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 247, "nodeType": "ExpressionStatement", "src": "2640:23:0"}, {"expression": {"id": 253, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 248, "name": "newPoll", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 214, "src": "2673:7:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage_ptr", "typeString": "struct SimpleVoting.Poll storage pointer"}}, "id": 250, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "2681:7:0", "memberName": "creator", "nodeType": "MemberAccess", "referencedDeclaration": 22, "src": "2673:15:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"expression": {"id": 251, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "2691:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 252, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2695:6:0", "memberName": "sender", "nodeType": "MemberAccess", "src": "2691:10:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "2673:28:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 254, "nodeType": "ExpressionStatement", "src": "2673:28:0"}, {"expression": {"id": 260, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 255, "name": "newPoll", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 214, "src": "2711:7:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage_ptr", "typeString": "struct SimpleVoting.Poll storage pointer"}}, "id": 257, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "2719:9:0", "memberName": "createdAt", "nodeType": "MemberAccess", "referencedDeclaration": 24, "src": "2711:17:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"expression": {"id": 258, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "2731:5:0", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 259, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2737:9:0", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "2731:15:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2711:35:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 261, "nodeType": "ExpressionStatement", "src": "2711:35:0"}, {"expression": {"id": 263, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "2792:11:0", "subExpression": {"id": 262, "name": "pollCount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 31, "src": "2792:9:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 264, "nodeType": "ExpressionStatement", "src": "2792:11:0"}, {"expression": {"arguments": [{"id": 270, "name": "pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 200, "src": "2843:6:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"baseExpression": {"id": 265, "name": "creatorP<PERSON>s", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 36, "src": "2813:12:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_array$_t_uint256_$dyn_storage_$", "typeString": "mapping(address => uint256[] storage ref)"}}, "id": 268, "indexExpression": {"expression": {"id": 266, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "2826:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 267, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2830:6:0", "memberName": "sender", "nodeType": "MemberAccess", "src": "2826:10:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2813:24:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage", "typeString": "uint256[] storage ref"}}, "id": 269, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2838:4:0", "memberName": "push", "nodeType": "MemberAccess", "src": "2813:29:0", "typeDescriptions": {"typeIdentifier": "t_function_arraypush_nonpayable$_t_array$_t_uint256_$dyn_storage_ptr_$_t_uint256_$returns$__$attached_to$_t_array$_t_uint256_$dyn_storage_ptr_$", "typeString": "function (uint256[] storage pointer,uint256)"}}, "id": 271, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2813:37:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 272, "nodeType": "ExpressionStatement", "src": "2813:37:0"}, {"eventCall": {"arguments": [{"id": 274, "name": "pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 200, "src": "2886:6:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 275, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "2894:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 276, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2898:6:0", "memberName": "sender", "nodeType": "MemberAccess", "src": "2894:10:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 277, "name": "_question", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 190, "src": "2906:9:0", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"expression": {"id": 278, "name": "_options", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 193, "src": "2917:8:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}, "id": 279, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2926:6:0", "memberName": "length", "nodeType": "MemberAccess", "src": "2917:15:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 273, "name": "PollCreated", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 46, "src": "2874:11:0", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$_t_address_$_t_string_memory_ptr_$_t_uint256_$returns$__$", "typeString": "function (uint256,address,string memory,uint256)"}}, "id": 280, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2874:59:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 281, "nodeType": "EmitStatement", "src": "2869:64:0"}]}, "documentation": {"id": 188, "nodeType": "StructuredDocumentation", "src": "2010:179:0", "text": " @dev Create a new poll\n @param _question The poll question\n @param _options Array of voting options\n @return pollId The ID of the created poll"}, "functionSelector": "7e3d71c3", "id": 283, "implemented": true, "kind": "function", "modifiers": [{"arguments": [{"id": 196, "name": "_question", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 190, "src": "2310:9:0", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 197, "name": "_options", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 193, "src": "2321:8:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}], "id": 198, "kind": "modifierInvocation", "modifierName": {"id": 195, "name": "validPollData", "nameLocations": ["2296:13:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 187, "src": "2296:13:0"}, "nodeType": "ModifierInvocation", "src": "2296:34:0"}], "name": "createPoll", "nameLocation": "2203:10:0", "nodeType": "FunctionDefinition", "parameters": {"id": 194, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 190, "mutability": "mutable", "name": "_question", "nameLocation": "2237:9:0", "nodeType": "VariableDeclaration", "scope": 283, "src": "2223:23:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 189, "name": "string", "nodeType": "ElementaryTypeName", "src": "2223:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 193, "mutability": "mutable", "name": "_options", "nameLocation": "2272:8:0", "nodeType": "VariableDeclaration", "scope": 283, "src": "2256:24:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string[]"}, "typeName": {"baseType": {"id": 191, "name": "string", "nodeType": "ElementaryTypeName", "src": "2256:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "id": 192, "nodeType": "ArrayTypeName", "src": "2256:8:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage_ptr", "typeString": "string[]"}}, "visibility": "internal"}], "src": "2213:73:0"}, "returnParameters": {"id": 201, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 200, "mutability": "mutable", "name": "pollId", "nameLocation": "2348:6:0", "nodeType": "VariableDeclaration", "scope": 283, "src": "2340:14:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 199, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2340:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2339:16:0"}, "scope": 506, "src": "2194:746:0", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 343, "nodeType": "Block", "src": "3318:258:0", "statements": [{"assignments": [306], "declarations": [{"constant": false, "id": 306, "mutability": "mutable", "name": "poll", "nameLocation": "3341:4:0", "nodeType": "VariableDeclaration", "scope": 343, "src": "3328:17:0", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage_ptr", "typeString": "struct SimpleVoting.Poll"}, "typeName": {"id": 305, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 304, "name": "Poll", "nameLocations": ["3328:4:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 25, "src": "3328:4:0"}, "referencedDeclaration": 25, "src": "3328:4:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage_ptr", "typeString": "struct SimpleVoting.Poll"}}, "visibility": "internal"}], "id": 310, "initialValue": {"baseExpression": {"id": 307, "name": "polls", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 29, "src": "3348:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Poll_$25_storage_$dyn_storage", "typeString": "struct SimpleVoting.Poll storage ref[] storage ref"}}, "id": 309, "indexExpression": {"id": 308, "name": "_pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 286, "src": "3354:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3348:14:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage", "typeString": "struct SimpleVoting.Poll storage ref"}}, "nodeType": "VariableDeclarationStatement", "src": "3328:34:0"}, {"expression": {"id": 318, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"expression": {"id": 311, "name": "poll", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 306, "src": "3381:4:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage_ptr", "typeString": "struct SimpleVoting.Poll storage pointer"}}, "id": 315, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "3386:8:0", "memberName": "hasVoted", "nodeType": "MemberAccess", "referencedDeclaration": 14, "src": "3381:13:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 316, "indexExpression": {"expression": {"id": 313, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3395:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 314, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3399:6:0", "memberName": "sender", "nodeType": "MemberAccess", "src": "3395:10:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "3381:25:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 317, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "3409:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "3381:32:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 319, "nodeType": "ExpressionStatement", "src": "3381:32:0"}, {"expression": {"id": 327, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"expression": {"id": 320, "name": "poll", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 306, "src": "3423:4:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage_ptr", "typeString": "struct SimpleVoting.Poll storage pointer"}}, "id": 324, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "3428:11:0", "memberName": "voterChoice", "nodeType": "MemberAccess", "referencedDeclaration": 18, "src": "3423:16:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 325, "indexExpression": {"expression": {"id": 322, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3440:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 323, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3444:6:0", "memberName": "sender", "nodeType": "MemberAccess", "src": "3440:10:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "3423:28:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 326, "name": "_optionIndex", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 288, "src": "3454:12:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3423:43:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 328, "nodeType": "ExpressionStatement", "src": "3423:43:0"}, {"expression": {"id": 334, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "3476:26:0", "subExpression": {"baseExpression": {"expression": {"id": 329, "name": "poll", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 306, "src": "3476:4:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage_ptr", "typeString": "struct SimpleVoting.Poll storage pointer"}}, "id": 332, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "3481:5:0", "memberName": "votes", "nodeType": "MemberAccess", "referencedDeclaration": 10, "src": "3476:10:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage", "typeString": "uint256[] storage ref"}}, "id": 333, "indexExpression": {"id": 331, "name": "_optionIndex", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 288, "src": "3487:12:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "3476:24:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 335, "nodeType": "ExpressionStatement", "src": "3476:26:0"}, {"eventCall": {"arguments": [{"id": 337, "name": "_pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 286, "src": "3535:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 338, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3544:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 339, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3548:6:0", "memberName": "sender", "nodeType": "MemberAccess", "src": "3544:10:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 340, "name": "_optionIndex", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 288, "src": "3556:12:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 336, "name": "VoteCast", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 54, "src": "3526:8:0", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (uint256,address,uint256)"}}, "id": 341, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3526:43:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 342, "nodeType": "EmitStatement", "src": "3521:48:0"}]}, "documentation": {"id": 284, "nodeType": "StructuredDocumentation", "src": "2946:137:0", "text": " @dev Cast a vote on a poll\n @param _pollId The poll ID\n @param _optionIndex The index of the chosen option"}, "functionSelector": "b384abef", "id": 344, "implemented": true, "kind": "function", "modifiers": [{"arguments": [{"id": 291, "name": "_pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 286, "src": "3201:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 292, "kind": "modifierInvocation", "modifierName": {"id": 290, "name": "pollExists", "nameLocations": ["3190:10:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 74, "src": "3190:10:0"}, "nodeType": "ModifierInvocation", "src": "3190:19:0"}, {"arguments": [{"id": 294, "name": "_pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 286, "src": "3230:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 295, "kind": "modifierInvocation", "modifierName": {"id": 293, "name": "pollActive", "nameLocations": ["3219:10:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 88, "src": "3219:10:0"}, "nodeType": "ModifierInvocation", "src": "3219:19:0"}, {"arguments": [{"id": 297, "name": "_pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 286, "src": "3260:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 298, "kind": "modifierInvocation", "modifierName": {"id": 296, "name": "hasNotVoted", "nameLocations": ["3248:11:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 106, "src": "3248:11:0"}, "nodeType": "ModifierInvocation", "src": "3248:20:0"}, {"arguments": [{"id": 300, "name": "_pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 286, "src": "3290:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 301, "name": "_optionIndex", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 288, "src": "3299:12:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 302, "kind": "modifierInvocation", "modifierName": {"id": 299, "name": "validOption", "nameLocations": ["3278:11:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 125, "src": "3278:11:0"}, "nodeType": "ModifierInvocation", "src": "3278:34:0"}], "name": "vote", "nameLocation": "3097:4:0", "nodeType": "FunctionDefinition", "parameters": {"id": 289, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 286, "mutability": "mutable", "name": "_pollId", "nameLocation": "3119:7:0", "nodeType": "VariableDeclaration", "scope": 344, "src": "3111:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 285, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3111:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 288, "mutability": "mutable", "name": "_optionIndex", "nameLocation": "3144:12:0", "nodeType": "VariableDeclaration", "scope": 344, "src": "3136:20:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 287, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3136:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3101:61:0"}, "returnParameters": {"id": 303, "nodeType": "ParameterList", "parameters": [], "src": "3318:0:0"}, "scope": 506, "src": "3088:488:0", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 388, "nodeType": "Block", "src": "4283:236:0", "statements": [{"assignments": [369], "declarations": [{"constant": false, "id": 369, "mutability": "mutable", "name": "poll", "nameLocation": "4306:4:0", "nodeType": "VariableDeclaration", "scope": 388, "src": "4293:17:0", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage_ptr", "typeString": "struct SimpleVoting.Poll"}, "typeName": {"id": 368, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 367, "name": "Poll", "nameLocations": ["4293:4:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 25, "src": "4293:4:0"}, "referencedDeclaration": 25, "src": "4293:4:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage_ptr", "typeString": "struct SimpleVoting.Poll"}}, "visibility": "internal"}], "id": 373, "initialValue": {"baseExpression": {"id": 370, "name": "polls", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 29, "src": "4313:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Poll_$25_storage_$dyn_storage", "typeString": "struct SimpleVoting.Poll storage ref[] storage ref"}}, "id": 372, "indexExpression": {"id": 371, "name": "_pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 347, "src": "4319:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "4313:14:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage", "typeString": "struct SimpleVoting.Poll storage ref"}}, "nodeType": "VariableDeclarationStatement", "src": "4293:34:0"}, {"expression": {"components": [{"expression": {"id": 374, "name": "poll", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 369, "src": "4358:4:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage_ptr", "typeString": "struct SimpleVoting.Poll storage pointer"}}, "id": 375, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4363:8:0", "memberName": "question", "nodeType": "MemberAccess", "referencedDeclaration": 4, "src": "4358:13:0", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, {"expression": {"id": 376, "name": "poll", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 369, "src": "4385:4:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage_ptr", "typeString": "struct SimpleVoting.Poll storage pointer"}}, "id": 377, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4390:7:0", "memberName": "options", "nodeType": "MemberAccess", "referencedDeclaration": 7, "src": "4385:12:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage", "typeString": "string storage ref[] storage ref"}}, {"expression": {"id": 378, "name": "poll", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 369, "src": "4411:4:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage_ptr", "typeString": "struct SimpleVoting.Poll storage pointer"}}, "id": 379, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4416:5:0", "memberName": "votes", "nodeType": "MemberAccess", "referencedDeclaration": 10, "src": "4411:10:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage", "typeString": "uint256[] storage ref"}}, {"expression": {"id": 380, "name": "poll", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 369, "src": "4435:4:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage_ptr", "typeString": "struct SimpleVoting.Poll storage pointer"}}, "id": 381, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4440:8:0", "memberName": "isActive", "nodeType": "MemberAccess", "referencedDeclaration": 20, "src": "4435:13:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"expression": {"id": 382, "name": "poll", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 369, "src": "4462:4:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage_ptr", "typeString": "struct SimpleVoting.Poll storage pointer"}}, "id": 383, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4467:7:0", "memberName": "creator", "nodeType": "MemberAccess", "referencedDeclaration": 22, "src": "4462:12:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"expression": {"id": 384, "name": "poll", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 369, "src": "4488:4:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage_ptr", "typeString": "struct SimpleVoting.Poll storage pointer"}}, "id": 385, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4493:9:0", "memberName": "createdAt", "nodeType": "MemberAccess", "referencedDeclaration": 24, "src": "4488:14:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 386, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "4344:168:0", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_string_storage_$_t_array$_t_string_storage_$dyn_storage_$_t_array$_t_uint256_$dyn_storage_$_t_bool_$_t_address_$_t_uint256_$", "typeString": "tuple(string storage ref,string storage ref[] storage ref,uint256[] storage ref,bool,address,uint256)"}}, "functionReturnParameters": 366, "id": 387, "nodeType": "Return", "src": "4337:175:0"}]}, "documentation": {"id": 345, "nodeType": "StructuredDocumentation", "src": "3582:377:0", "text": " @dev Get poll information\n @param _pollId The poll ID\n @return question The poll question\n @return options Array of voting options\n @return votes Array of vote counts for each option\n @return isActive Whether the poll is active\n @return creator The poll creator address\n @return createdAt The poll creation timestamp"}, "functionSelector": "1a8cbcaa", "id": 389, "implemented": true, "kind": "function", "modifiers": [{"arguments": [{"id": 350, "name": "_pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 347, "src": "4047:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 351, "kind": "modifierInvocation", "modifierName": {"id": 349, "name": "pollExists", "nameLocations": ["4036:10:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 74, "src": "4036:10:0"}, "nodeType": "ModifierInvocation", "src": "4036:19:0"}], "name": "getPoll", "nameLocation": "3973:7:0", "nodeType": "FunctionDefinition", "parameters": {"id": 348, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 347, "mutability": "mutable", "name": "_pollId", "nameLocation": "3989:7:0", "nodeType": "VariableDeclaration", "scope": 389, "src": "3981:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 346, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3981:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3980:17:0"}, "returnParameters": {"id": 366, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 353, "mutability": "mutable", "name": "question", "nameLocation": "4100:8:0", "nodeType": "VariableDeclaration", "scope": 389, "src": "4086:22:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 352, "name": "string", "nodeType": "ElementaryTypeName", "src": "4086:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 356, "mutability": "mutable", "name": "options", "nameLocation": "4138:7:0", "nodeType": "VariableDeclaration", "scope": 389, "src": "4122:23:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string[]"}, "typeName": {"baseType": {"id": 354, "name": "string", "nodeType": "ElementaryTypeName", "src": "4122:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "id": 355, "nodeType": "ArrayTypeName", "src": "4122:8:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage_ptr", "typeString": "string[]"}}, "visibility": "internal"}, {"constant": false, "id": 359, "mutability": "mutable", "name": "votes", "nameLocation": "4176:5:0", "nodeType": "VariableDeclaration", "scope": 389, "src": "4159:22:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[]"}, "typeName": {"baseType": {"id": 357, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4159:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 358, "nodeType": "ArrayTypeName", "src": "4159:9:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}, "visibility": "internal"}, {"constant": false, "id": 361, "mutability": "mutable", "name": "isActive", "nameLocation": "4200:8:0", "nodeType": "VariableDeclaration", "scope": 389, "src": "4195:13:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 360, "name": "bool", "nodeType": "ElementaryTypeName", "src": "4195:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 363, "mutability": "mutable", "name": "creator", "nameLocation": "4230:7:0", "nodeType": "VariableDeclaration", "scope": 389, "src": "4222:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 362, "name": "address", "nodeType": "ElementaryTypeName", "src": "4222:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 365, "mutability": "mutable", "name": "createdAt", "nameLocation": "4259:9:0", "nodeType": "VariableDeclaration", "scope": 389, "src": "4251:17:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 364, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4251:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "4072:206:0"}, "scope": 506, "src": "3964:555:0", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 398, "nodeType": "Block", "src": "4675:36:0", "statements": [{"expression": {"expression": {"id": 395, "name": "polls", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 29, "src": "4692:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Poll_$25_storage_$dyn_storage", "typeString": "struct SimpleVoting.Poll storage ref[] storage ref"}}, "id": 396, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4698:6:0", "memberName": "length", "nodeType": "MemberAccess", "src": "4692:12:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 394, "id": 397, "nodeType": "Return", "src": "4685:19:0"}]}, "documentation": {"id": 390, "nodeType": "StructuredDocumentation", "src": "4525:89:0", "text": " @dev Get the total number of polls\n @return The total poll count"}, "functionSelector": "11a52746", "id": 399, "implemented": true, "kind": "function", "modifiers": [], "name": "getPollCount", "nameLocation": "4628:12:0", "nodeType": "FunctionDefinition", "parameters": {"id": 391, "nodeType": "ParameterList", "parameters": [], "src": "4640:2:0"}, "returnParameters": {"id": 394, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 393, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 399, "src": "4666:7:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 392, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4666:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "4665:9:0"}, "scope": 506, "src": "4619:92:0", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 419, "nodeType": "Block", "src": "5019:54:0", "statements": [{"expression": {"baseExpression": {"expression": {"baseExpression": {"id": 412, "name": "polls", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 29, "src": "5036:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Poll_$25_storage_$dyn_storage", "typeString": "struct SimpleVoting.Poll storage ref[] storage ref"}}, "id": 414, "indexExpression": {"id": 413, "name": "_pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 402, "src": "5042:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5036:14:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage", "typeString": "struct SimpleVoting.Poll storage ref"}}, "id": 415, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5051:8:0", "memberName": "hasVoted", "nodeType": "MemberAccess", "referencedDeclaration": 14, "src": "5036:23:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 417, "indexExpression": {"id": 416, "name": "_user", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 404, "src": "5060:5:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5036:30:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 411, "id": 418, "nodeType": "Return", "src": "5029:37:0"}]}, "documentation": {"id": 400, "nodeType": "StructuredDocumentation", "src": "4717:172:0", "text": " @dev Check if a user has voted on a poll\n @param _pollId The poll ID\n @param _user The user address\n @return Whether the user has voted"}, "functionSelector": "dc296ae1", "id": 420, "implemented": true, "kind": "function", "modifiers": [{"arguments": [{"id": 407, "name": "_pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 402, "src": "4995:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 408, "kind": "modifierInvocation", "modifierName": {"id": 406, "name": "pollExists", "nameLocations": ["4984:10:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 74, "src": "4984:10:0"}, "nodeType": "ModifierInvocation", "src": "4984:19:0"}], "name": "hasUserVoted", "nameLocation": "4903:12:0", "nodeType": "FunctionDefinition", "parameters": {"id": 405, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 402, "mutability": "mutable", "name": "_pollId", "nameLocation": "4933:7:0", "nodeType": "VariableDeclaration", "scope": 420, "src": "4925:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 401, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4925:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 404, "mutability": "mutable", "name": "_user", "nameLocation": "4958:5:0", "nodeType": "VariableDeclaration", "scope": 420, "src": "4950:13:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 403, "name": "address", "nodeType": "ElementaryTypeName", "src": "4950:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "4915:54:0"}, "returnParameters": {"id": 411, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 410, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 420, "src": "5013:4:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 409, "name": "bool", "nodeType": "ElementaryTypeName", "src": "5013:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "5012:6:0"}, "scope": 506, "src": "4894:179:0", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 450, "nodeType": "Block", "src": "5383:128:0", "statements": [{"expression": {"arguments": [{"baseExpression": {"expression": {"baseExpression": {"id": 434, "name": "polls", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 29, "src": "5401:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Poll_$25_storage_$dyn_storage", "typeString": "struct SimpleVoting.Poll storage ref[] storage ref"}}, "id": 436, "indexExpression": {"id": 435, "name": "_pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 423, "src": "5407:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5401:14:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage", "typeString": "struct SimpleVoting.Poll storage ref"}}, "id": 437, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5416:8:0", "memberName": "hasVoted", "nodeType": "MemberAccess", "referencedDeclaration": 14, "src": "5401:23:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 439, "indexExpression": {"id": 438, "name": "_user", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 425, "src": "5425:5:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5401:30:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "5573657220686173206e6f7420766f746564", "id": 440, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5433:20:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_ec1b155bf1042601d514070206494c49df2fdba4a1da92fad635a0f6e770613f", "typeString": "literal_string \"User has not voted\""}, "value": "User has not voted"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_ec1b155bf1042601d514070206494c49df2fdba4a1da92fad635a0f6e770613f", "typeString": "literal_string \"User has not voted\""}], "id": 433, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "5393:7:0", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 441, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5393:61:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 442, "nodeType": "ExpressionStatement", "src": "5393:61:0"}, {"expression": {"baseExpression": {"expression": {"baseExpression": {"id": 443, "name": "polls", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 29, "src": "5471:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Poll_$25_storage_$dyn_storage", "typeString": "struct SimpleVoting.Poll storage ref[] storage ref"}}, "id": 445, "indexExpression": {"id": 444, "name": "_pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 423, "src": "5477:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5471:14:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage", "typeString": "struct SimpleVoting.Poll storage ref"}}, "id": 446, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5486:11:0", "memberName": "voterChoice", "nodeType": "MemberAccess", "referencedDeclaration": 18, "src": "5471:26:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 448, "indexExpression": {"id": 447, "name": "_user", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 425, "src": "5498:5:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5471:33:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 432, "id": 449, "nodeType": "Return", "src": "5464:40:0"}]}, "documentation": {"id": 421, "nodeType": "StructuredDocumentation", "src": "5079:172:0", "text": " @dev Get a user's vote choice\n @param _pollId The poll ID\n @param _user The user address\n @return The user's vote choice (option index)"}, "functionSelector": "03c7881a", "id": 451, "implemented": true, "kind": "function", "modifiers": [{"arguments": [{"id": 428, "name": "_pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 423, "src": "5356:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 429, "kind": "modifierInvocation", "modifierName": {"id": 427, "name": "pollExists", "nameLocations": ["5345:10:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 74, "src": "5345:10:0"}, "nodeType": "ModifierInvocation", "src": "5345:19:0"}], "name": "getUserVote", "nameLocation": "5265:11:0", "nodeType": "FunctionDefinition", "parameters": {"id": 426, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 423, "mutability": "mutable", "name": "_pollId", "nameLocation": "5294:7:0", "nodeType": "VariableDeclaration", "scope": 451, "src": "5286:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 422, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "5286:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 425, "mutability": "mutable", "name": "_user", "nameLocation": "5319:5:0", "nodeType": "VariableDeclaration", "scope": 451, "src": "5311:13:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 424, "name": "address", "nodeType": "ElementaryTypeName", "src": "5311:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "5276:54:0"}, "returnParameters": {"id": 432, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 431, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 451, "src": "5374:7:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 430, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "5374:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "5373:9:0"}, "scope": 506, "src": "5256:255:0", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 490, "nodeType": "Block", "src": "5724:231:0", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 467, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"baseExpression": {"id": 461, "name": "polls", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 29, "src": "5742:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Poll_$25_storage_$dyn_storage", "typeString": "struct SimpleVoting.Poll storage ref[] storage ref"}}, "id": 463, "indexExpression": {"id": 462, "name": "_pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 454, "src": "5748:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5742:14:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage", "typeString": "struct SimpleVoting.Poll storage ref"}}, "id": 464, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5757:7:0", "memberName": "creator", "nodeType": "MemberAccess", "referencedDeclaration": 22, "src": "5742:22:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"id": 465, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "5768:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 466, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5772:6:0", "memberName": "sender", "nodeType": "MemberAccess", "src": "5768:10:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "5742:36:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4f6e6c792063726561746f722063616e20746f67676c6520737461747573", "id": 468, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5780:32:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_39d63f22c983e0a835c80b26c00e97ac46e50731b96804551d851bf715b5fd2c", "typeString": "literal_string \"Only creator can toggle status\""}, "value": "Only creator can toggle status"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_39d63f22c983e0a835c80b26c00e97ac46e50731b96804551d851bf715b5fd2c", "typeString": "literal_string \"Only creator can toggle status\""}], "id": 460, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "5734:7:0", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 469, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5734:79:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 470, "nodeType": "ExpressionStatement", "src": "5734:79:0"}, {"expression": {"id": 480, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"baseExpression": {"id": 471, "name": "polls", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 29, "src": "5832:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Poll_$25_storage_$dyn_storage", "typeString": "struct SimpleVoting.Poll storage ref[] storage ref"}}, "id": 473, "indexExpression": {"id": 472, "name": "_pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 454, "src": "5838:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5832:14:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage", "typeString": "struct SimpleVoting.Poll storage ref"}}, "id": 474, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "5847:8:0", "memberName": "isActive", "nodeType": "MemberAccess", "referencedDeclaration": 20, "src": "5832:23:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 479, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "5858:24:0", "subExpression": {"expression": {"baseExpression": {"id": 475, "name": "polls", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 29, "src": "5859:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Poll_$25_storage_$dyn_storage", "typeString": "struct SimpleVoting.Poll storage ref[] storage ref"}}, "id": 477, "indexExpression": {"id": 476, "name": "_pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 454, "src": "5865:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5859:14:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage", "typeString": "struct SimpleVoting.Poll storage ref"}}, "id": 478, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5874:8:0", "memberName": "isActive", "nodeType": "MemberAccess", "referencedDeclaration": 20, "src": "5859:23:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "5832:50:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 481, "nodeType": "ExpressionStatement", "src": "5832:50:0"}, {"eventCall": {"arguments": [{"id": 483, "name": "_pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 454, "src": "5915:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"baseExpression": {"id": 484, "name": "polls", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 29, "src": "5924:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Poll_$25_storage_$dyn_storage", "typeString": "struct SimpleVoting.Poll storage ref[] storage ref"}}, "id": 486, "indexExpression": {"id": 485, "name": "_pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 454, "src": "5930:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5924:14:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Poll_$25_storage", "typeString": "struct SimpleVoting.Poll storage ref"}}, "id": 487, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5939:8:0", "memberName": "isActive", "nodeType": "MemberAccess", "referencedDeclaration": 20, "src": "5924:23:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 482, "name": "PollStatusChanged", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 60, "src": "5897:17:0", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$_t_bool_$returns$__$", "typeString": "function (uint256,bool)"}}, "id": 488, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5897:51:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 489, "nodeType": "EmitStatement", "src": "5892:56:0"}]}, "documentation": {"id": 452, "nodeType": "StructuredDocumentation", "src": "5517:107:0", "text": " @dev Toggle poll active status (only creator can call)\n @param _pollId The poll ID"}, "functionSelector": "251e02d7", "id": 491, "implemented": true, "kind": "function", "modifiers": [{"arguments": [{"id": 457, "name": "_pollId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 454, "src": "5710:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 458, "kind": "modifierInvocation", "modifierName": {"id": 456, "name": "pollExists", "nameLocations": ["5699:10:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 74, "src": "5699:10:0"}, "nodeType": "ModifierInvocation", "src": "5699:19:0"}], "name": "togglePollStatus", "nameLocation": "5638:16:0", "nodeType": "FunctionDefinition", "parameters": {"id": 455, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 454, "mutability": "mutable", "name": "_pollId", "nameLocation": "5663:7:0", "nodeType": "VariableDeclaration", "scope": 491, "src": "5655:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 453, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "5655:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "5654:17:0"}, "returnParameters": {"id": 459, "nodeType": "ParameterList", "parameters": [], "src": "5724:0:0"}, "scope": 506, "src": "5629:326:0", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 504, "nodeType": "Block", "src": "6212:46:0", "statements": [{"expression": {"baseExpression": {"id": 500, "name": "creatorP<PERSON>s", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 36, "src": "6229:12:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_array$_t_uint256_$dyn_storage_$", "typeString": "mapping(address => uint256[] storage ref)"}}, "id": 502, "indexExpression": {"id": 501, "name": "_creator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 494, "src": "6242:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "6229:22:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage", "typeString": "uint256[] storage ref"}}, "functionReturnParameters": 499, "id": 503, "nodeType": "Return", "src": "6222:29:0"}]}, "documentation": {"id": 492, "nodeType": "StructuredDocumentation", "src": "5961:162:0", "text": " @dev Get polls created by a specific address\n @param _creator The creator address\n @return Array of poll IDs created by the address"}, "functionSelector": "ec81a17a", "id": 505, "implemented": true, "kind": "function", "modifiers": [], "name": "getCreatorPolls", "nameLocation": "6137:15:0", "nodeType": "FunctionDefinition", "parameters": {"id": 495, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 494, "mutability": "mutable", "name": "_creator", "nameLocation": "6161:8:0", "nodeType": "VariableDeclaration", "scope": 505, "src": "6153:16:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 493, "name": "address", "nodeType": "ElementaryTypeName", "src": "6153:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "6152:18:0"}, "returnParameters": {"id": 499, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 498, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 505, "src": "6194:16:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[]"}, "typeName": {"baseType": {"id": 496, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "6194:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 497, "nodeType": "ArrayTypeName", "src": "6194:9:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}, "visibility": "internal"}], "src": "6193:18:0"}, "scope": 506, "src": "6128:130:0", "stateMutability": "view", "virtual": false, "visibility": "external"}], "scope": 507, "src": "159:6101:0", "usedErrors": []}], "src": "32:6229:0"}, "id": 0}}, "contracts": {"contracts/SimpleVoting.sol": {"SimpleVoting": {"abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "pollId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "creator", "type": "address"}, {"indexed": false, "internalType": "string", "name": "question", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "optionCount", "type": "uint256"}], "name": "PollCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "pollId", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "isActive", "type": "bool"}], "name": "PollStatusChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "pollId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "voter", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "optionIndex", "type": "uint256"}], "name": "VoteCast", "type": "event"}, {"inputs": [{"internalType": "string", "name": "_question", "type": "string"}, {"internalType": "string[]", "name": "_options", "type": "string[]"}], "name": "createPoll", "outputs": [{"internalType": "uint256", "name": "pollId", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "creatorP<PERSON>s", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_creator", "type": "address"}], "name": "getCreatorPolls", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_pollId", "type": "uint256"}], "name": "getPoll", "outputs": [{"internalType": "string", "name": "question", "type": "string"}, {"internalType": "string[]", "name": "options", "type": "string[]"}, {"internalType": "uint256[]", "name": "votes", "type": "uint256[]"}, {"internalType": "bool", "name": "isActive", "type": "bool"}, {"internalType": "address", "name": "creator", "type": "address"}, {"internalType": "uint256", "name": "createdAt", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getPollCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_pollId", "type": "uint256"}, {"internalType": "address", "name": "_user", "type": "address"}], "name": "getUserVote", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_pollId", "type": "uint256"}, {"internalType": "address", "name": "_user", "type": "address"}], "name": "hasUserVoted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pollCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "polls", "outputs": [{"internalType": "string", "name": "question", "type": "string"}, {"internalType": "bool", "name": "isActive", "type": "bool"}, {"internalType": "address", "name": "creator", "type": "address"}, {"internalType": "uint256", "name": "createdAt", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_pollId", "type": "uint256"}], "name": "togglePollStatus", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_pollId", "type": "uint256"}, {"internalType": "uint256", "name": "_optionIndex", "type": "uint256"}], "name": "vote", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "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", "opcodes": "PUSH1 0x80 PUSH1 0x40 MSTORE CALLVALUE DUP1 ISZERO PUSH2 0x10 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x1440 DUP1 PUSH2 0x20 PUSH1 0x0 CODECOPY PUSH1 0x0 RETURN INVALID PUSH1 0x80 PUSH1 0x40 MSTORE CALLVALUE DUP1 ISZERO PUSH2 0x10 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH1 0x4 CALLDATASIZE LT PUSH2 0xA9 JUMPI PUSH1 0x0 CALLDATALOAD PUSH1 0xE0 SHR DUP1 PUSH4 0x8439E976 GT PUSH2 0x71 JUMPI DUP1 PUSH4 0x8439E976 EQ PUSH2 0x129 JUMPI DUP1 PUSH4 0x9207891D EQ PUSH2 0x13C JUMPI DUP1 PUSH4 0xAC2F0074 EQ PUSH2 0x145 JUMPI DUP1 PUSH4 0xB384ABEF EQ PUSH2 0x168 JUMPI DUP1 PUSH4 0xDC296AE1 EQ PUSH2 0x17B JUMPI DUP1 PUSH4 0xEC81A17A EQ PUSH2 0x19E JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP1 PUSH4 0x3C7881A EQ PUSH2 0xAE JUMPI DUP1 PUSH4 0x11A52746 EQ PUSH2 0xD4 JUMPI DUP1 PUSH4 0x1A8CBCAA EQ PUSH2 0xDC JUMPI DUP1 PUSH4 0x251E02D7 EQ PUSH2 0x101 JUMPI DUP1 PUSH4 0x7E3D71C3 EQ PUSH2 0x116 JUMPI JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH2 0xC1 PUSH2 0xBC CALLDATASIZE PUSH1 0x4 PUSH2 0xE6F JUMP JUMPDEST PUSH2 0x1BE JUMP JUMPDEST PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE PUSH1 0x20 ADD JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST PUSH1 0x0 SLOAD PUSH2 0xC1 JUMP JUMPDEST PUSH2 0xEF PUSH2 0xEA CALLDATASIZE PUSH1 0x4 PUSH2 0xE9B JUMP JUMPDEST PUSH2 0x2B0 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0xCB SWAP7 SWAP6 SWAP5 SWAP4 SWAP3 SWAP2 SWAP1 PUSH2 0xF35 JUMP JUMPDEST PUSH2 0x114 PUSH2 0x10F CALLDATASIZE PUSH1 0x4 PUSH2 0xE9B JUMP JUMPDEST PUSH2 0x4FA JUMP JUMPDEST STOP JUMPDEST PUSH2 0xC1 PUSH2 0x124 CALLDATASIZE PUSH1 0x4 PUSH2 0x1095 JUMP JUMPDEST PUSH2 0x67A JUMP JUMPDEST PUSH2 0xC1 PUSH2 0x137 CALLDATASIZE PUSH1 0x4 PUSH2 0x117C JUMP JUMPDEST PUSH2 0x93B JUMP JUMPDEST PUSH2 0xC1 PUSH1 0x1 SLOAD DUP2 JUMP JUMPDEST PUSH2 0x158 PUSH2 0x153 CALLDATASIZE PUSH1 0x4 PUSH2 0xE9B JUMP JUMPDEST PUSH2 0x96C JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0xCB SWAP5 SWAP4 SWAP3 SWAP2 SWAP1 PUSH2 0x11A6 JUMP JUMPDEST PUSH2 0x114 PUSH2 0x176 CALLDATASIZE PUSH1 0x4 PUSH2 0x11DE JUMP JUMPDEST PUSH2 0xA42 JUMP JUMPDEST PUSH2 0x18E PUSH2 0x189 CALLDATASIZE PUSH1 0x4 PUSH2 0xE6F JUMP JUMPDEST PUSH2 0xC6F JUMP JUMPDEST PUSH1 0x40 MLOAD SWAP1 ISZERO ISZERO DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0xCB JUMP JUMPDEST PUSH2 0x1B1 PUSH2 0x1AC CALLDATASIZE PUSH1 0x4 PUSH2 0x1200 JUMP JUMPDEST PUSH2 0xCDB JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0xCB SWAP2 SWAP1 PUSH2 0x1222 JUMP JUMPDEST PUSH1 0x0 DUP1 SLOAD DUP4 SWAP1 DUP2 LT PUSH2 0x1EB JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH2 0x1E2 SWAP1 PUSH2 0x1235 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH1 0x0 DUP5 DUP2 SLOAD DUP2 LT PUSH2 0x1FE JUMPI PUSH2 0x1FE PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 DUP1 DUP4 KECCAK256 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP8 AND DUP5 MSTORE PUSH1 0x3 PUSH1 0x7 SWAP1 SWAP4 MUL ADD SWAP2 SWAP1 SWAP2 ADD SWAP1 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD PUSH1 0xFF AND PUSH2 0x26B JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x12 PUSH1 0x24 DUP3 ADD MSTORE PUSH18 0x155CD95C881A185CC81B9BDD081D9BDD1959 PUSH1 0x72 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x1E2 JUMP JUMPDEST PUSH1 0x0 DUP5 DUP2 SLOAD DUP2 LT PUSH2 0x27E JUMPI PUSH2 0x27E PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 DUP1 DUP4 KECCAK256 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP8 AND DUP5 MSTORE PUSH1 0x4 PUSH1 0x7 SWAP1 SWAP4 MUL ADD SWAP2 SWAP1 SWAP2 ADD SWAP1 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x60 DUP1 PUSH1 0x60 PUSH1 0x0 DUP1 PUSH1 0x0 DUP7 PUSH1 0x0 DUP1 SLOAD SWAP1 POP DUP2 LT PUSH2 0x2DF JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH2 0x1E2 SWAP1 PUSH2 0x1235 JUMP JUMPDEST PUSH1 0x0 DUP1 DUP10 DUP2 SLOAD DUP2 LT PUSH2 0x2F3 JUMPI PUSH2 0x2F3 PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 SWAP1 SWAP2 KECCAK256 PUSH1 0x7 SWAP1 SWAP2 MUL ADD PUSH1 0x5 DUP2 ADD SLOAD PUSH1 0x6 DUP3 ADD SLOAD DUP3 SLOAD SWAP3 SWAP4 POP DUP4 SWAP3 PUSH1 0x1 DUP5 ADD SWAP3 PUSH1 0x2 DUP6 ADD SWAP3 PUSH1 0xFF DUP3 AND SWAP3 PUSH2 0x100 SWAP1 SWAP3 DIV PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND SWAP2 DUP7 SWAP1 PUSH2 0x33E SWAP1 PUSH2 0x1278 JUMP JUMPDEST DUP1 PUSH1 0x1F ADD PUSH1 0x20 DUP1 SWAP2 DIV MUL PUSH1 0x20 ADD PUSH1 0x40 MLOAD SWAP1 DUP2 ADD PUSH1 0x40 MSTORE DUP1 SWAP3 SWAP2 SWAP1 DUP2 DUP2 MSTORE PUSH1 0x20 ADD DUP3 DUP1 SLOAD PUSH2 0x36A SWAP1 PUSH2 0x1278 JUMP JUMPDEST DUP1 ISZERO PUSH2 0x3B7 JUMPI DUP1 PUSH1 0x1F LT PUSH2 0x38C JUMPI PUSH2 0x100 DUP1 DUP4 SLOAD DIV MUL DUP4 MSTORE SWAP2 PUSH1 0x20 ADD SWAP2 PUSH2 0x3B7 JUMP JUMPDEST DUP3 ADD SWAP2 SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 JUMPDEST DUP2 SLOAD DUP2 MSTORE SWAP1 PUSH1 0x1 ADD SWAP1 PUSH1 0x20 ADD DUP1 DUP4 GT PUSH2 0x39A JUMPI DUP3 SWAP1 SUB PUSH1 0x1F AND DUP3 ADD SWAP2 JUMPDEST POP POP POP POP POP SWAP6 POP DUP5 DUP1 SLOAD DUP1 PUSH1 0x20 MUL PUSH1 0x20 ADD PUSH1 0x40 MLOAD SWAP1 DUP2 ADD PUSH1 0x40 MSTORE DUP1 SWAP3 SWAP2 SWAP1 DUP2 DUP2 MSTORE PUSH1 0x20 ADD PUSH1 0x0 SWAP1 JUMPDEST DUP3 DUP3 LT ISZERO PUSH2 0x48B JUMPI DUP4 DUP3 SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 ADD DUP1 SLOAD PUSH2 0x3FE SWAP1 PUSH2 0x1278 JUMP JUMPDEST DUP1 PUSH1 0x1F ADD PUSH1 0x20 DUP1 SWAP2 DIV MUL PUSH1 0x20 ADD PUSH1 0x40 MLOAD SWAP1 DUP2 ADD PUSH1 0x40 MSTORE DUP1 SWAP3 SWAP2 SWAP1 DUP2 DUP2 MSTORE PUSH1 0x20 ADD DUP3 DUP1 SLOAD PUSH2 0x42A SWAP1 PUSH2 0x1278 JUMP JUMPDEST DUP1 ISZERO PUSH2 0x477 JUMPI DUP1 PUSH1 0x1F LT PUSH2 0x44C JUMPI PUSH2 0x100 DUP1 DUP4 SLOAD DIV MUL DUP4 MSTORE SWAP2 PUSH1 0x20 ADD SWAP2 PUSH2 0x477 JUMP JUMPDEST DUP3 ADD SWAP2 SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 JUMPDEST DUP2 SLOAD DUP2 MSTORE SWAP1 PUSH1 0x1 ADD SWAP1 PUSH1 0x20 ADD DUP1 DUP4 GT PUSH2 0x45A JUMPI DUP3 SWAP1 SUB PUSH1 0x1F AND DUP3 ADD SWAP2 JUMPDEST POP POP POP POP POP DUP2 MSTORE PUSH1 0x20 ADD SWAP1 PUSH1 0x1 ADD SWAP1 PUSH2 0x3DF JUMP JUMPDEST POP POP POP POP SWAP5 POP DUP4 DUP1 SLOAD DUP1 PUSH1 0x20 MUL PUSH1 0x20 ADD PUSH1 0x40 MLOAD SWAP1 DUP2 ADD PUSH1 0x40 MSTORE DUP1 SWAP3 SWAP2 SWAP1 DUP2 DUP2 MSTORE PUSH1 0x20 ADD DUP3 DUP1 SLOAD DUP1 ISZERO PUSH2 0x4DC JUMPI PUSH1 0x20 MUL DUP3 ADD SWAP2 SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 JUMPDEST DUP2 SLOAD DUP2 MSTORE PUSH1 0x20 ADD SWAP1 PUSH1 0x1 ADD SWAP1 DUP1 DUP4 GT PUSH2 0x4C8 JUMPI JUMPDEST POP POP POP POP POP SWAP4 POP SWAP8 POP SWAP8 POP SWAP8 POP SWAP8 POP SWAP8 POP SWAP8 POP POP POP SWAP2 SWAP4 SWAP6 POP SWAP2 SWAP4 SWAP6 JUMP JUMPDEST PUSH1 0x0 SLOAD DUP2 SWAP1 DUP2 LT PUSH2 0x51D JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH2 0x1E2 SWAP1 PUSH2 0x1235 JUMP JUMPDEST CALLER PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH1 0x0 DUP4 DUP2 SLOAD DUP2 LT PUSH2 0x53A JUMPI PUSH2 0x53A PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 SWAP1 SWAP2 KECCAK256 PUSH1 0x7 SWAP1 SWAP2 MUL ADD PUSH1 0x5 ADD SLOAD PUSH2 0x100 SWAP1 DIV PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND EQ PUSH2 0x5A9 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x1E PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x4F6E6C792063726561746F722063616E20746F67676C65207374617475730000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x1E2 JUMP JUMPDEST PUSH1 0x0 DUP3 DUP2 SLOAD DUP2 LT PUSH2 0x5BC JUMPI PUSH2 0x5BC PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 DUP3 KECCAK256 PUSH1 0x5 PUSH1 0x7 SWAP1 SWAP3 MUL ADD ADD SLOAD DUP2 SLOAD PUSH1 0xFF SWAP1 SWAP2 AND ISZERO SWAP2 SWAP1 DUP5 SWAP1 DUP2 LT PUSH2 0x5E9 JUMPI PUSH2 0x5E9 PUSH2 0x1262 JUMP JUMPDEST SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x7 MUL ADD PUSH1 0x5 ADD PUSH1 0x0 PUSH2 0x100 EXP DUP2 SLOAD DUP2 PUSH1 0xFF MUL NOT AND SWAP1 DUP4 ISZERO ISZERO MUL OR SWAP1 SSTORE POP DUP2 PUSH32 0xB7B8C0761D078843A3E84D05266AB3ECA7A488CD293CCE2EAC1441C9287D4B53 PUSH1 0x0 DUP5 DUP2 SLOAD DUP2 LT PUSH2 0x646 JUMPI PUSH2 0x646 PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 SWAP1 SWAP2 KECCAK256 PUSH1 0x5 PUSH1 0x7 SWAP1 SWAP3 MUL ADD ADD SLOAD PUSH1 0x40 MLOAD PUSH2 0x66E SWAP2 PUSH1 0xFF AND ISZERO ISZERO DUP2 MSTORE PUSH1 0x20 ADD SWAP1 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG2 POP POP JUMP JUMPDEST PUSH1 0x0 DUP3 DUP3 PUSH1 0x0 DUP3 MLOAD GT PUSH2 0x6CF JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x18 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x5175657374696F6E2063616E6E6F7420626520656D7074790000000000000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x1E2 JUMP JUMPDEST PUSH1 0x2 DUP2 MLOAD LT ISZERO PUSH2 0x721 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x1B PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x4174206C656173742032206F7074696F6E732072657175697265640000000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x1E2 JUMP JUMPDEST PUSH1 0xA DUP2 MLOAD GT ISZERO PUSH2 0x766 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x10 PUSH1 0x24 DUP3 ADD MSTORE PUSH16 0x546F6F206D616E79206F7074696F6E73 PUSH1 0x80 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x1E2 JUMP JUMPDEST PUSH1 0x0 JUMPDEST DUP2 MLOAD DUP2 LT ISZERO PUSH2 0x7E7 JUMPI PUSH1 0x0 DUP3 DUP3 DUP2 MLOAD DUP2 LT PUSH2 0x786 JUMPI PUSH2 0x786 PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x20 MUL PUSH1 0x20 ADD ADD MLOAD MLOAD GT PUSH2 0x7D5 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x16 PUSH1 0x24 DUP3 ADD MSTORE PUSH22 0x4F7074696F6E2063616E6E6F7420626520656D707479 PUSH1 0x50 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x1E2 JUMP JUMPDEST DUP1 PUSH2 0x7DF DUP2 PUSH2 0x12B2 JUMP JUMPDEST SWAP2 POP POP PUSH2 0x769 JUMP JUMPDEST POP PUSH1 0x0 DUP1 SLOAD PUSH1 0x1 DUP2 ADD DUP1 DUP4 SSTORE DUP3 DUP1 MSTORE SWAP1 SWAP5 POP DUP2 SWAP1 DUP6 SWAP1 DUP2 LT PUSH2 0x80B JUMPI PUSH2 0x80B PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 SWAP1 SWAP2 KECCAK256 PUSH1 0x7 SWAP1 SWAP2 MUL ADD SWAP1 POP DUP1 PUSH2 0x828 DUP8 DUP3 PUSH2 0x1328 JUMP JUMPDEST POP DUP5 MLOAD PUSH2 0x83E SWAP1 PUSH1 0x1 DUP4 ADD SWAP1 PUSH1 0x20 DUP9 ADD SWAP1 PUSH2 0xD47 JUMP JUMPDEST POP DUP5 MLOAD PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT ISZERO PUSH2 0x859 JUMPI PUSH2 0x859 PUSH2 0xFDE JUMP JUMPDEST PUSH1 0x40 MLOAD SWAP1 DUP1 DUP3 MSTORE DUP1 PUSH1 0x20 MUL PUSH1 0x20 ADD DUP3 ADD PUSH1 0x40 MSTORE DUP1 ISZERO PUSH2 0x882 JUMPI DUP2 PUSH1 0x20 ADD PUSH1 0x20 DUP3 MUL DUP1 CALLDATASIZE DUP4 CALLDATACOPY ADD SWAP1 POP JUMPDEST POP DUP1 MLOAD PUSH2 0x899 SWAP2 PUSH1 0x2 DUP5 ADD SWAP2 PUSH1 0x20 SWAP1 SWAP2 ADD SWAP1 PUSH2 0xD9D JUMP JUMPDEST POP PUSH1 0x5 DUP2 ADD DUP1 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA8 SHL SUB NOT SWAP1 SWAP2 AND PUSH2 0x100 CALLER MUL OR DUP2 OR SWAP1 SWAP2 SSTORE TIMESTAMP PUSH1 0x6 DUP4 ADD SSTORE DUP1 SLOAD SWAP1 PUSH1 0x0 PUSH2 0x8CD DUP4 PUSH2 0x12B2 JUMP JUMPDEST SWAP1 SWAP2 SSTORE POP POP CALLER PUSH1 0x0 DUP2 DUP2 MSTORE PUSH1 0x2 PUSH1 0x20 SWAP1 DUP2 MSTORE PUSH1 0x40 DUP1 DUP4 KECCAK256 DUP1 SLOAD PUSH1 0x1 DUP2 ADD DUP3 SSTORE SWAP1 DUP5 MSTORE SWAP2 SWAP1 SWAP3 KECCAK256 ADD DUP7 SWAP1 SSTORE DUP7 MLOAD SWAP1 MLOAD DUP7 SWAP2 PUSH32 0x9DBAFD4116DCEAD87280B4BAD16E92C851A647FBF87A77E05CF6B29C8FCDC33C SWAP2 PUSH2 0x92A SWAP2 DUP12 SWAP2 PUSH2 0x13E8 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG3 POP POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x2 PUSH1 0x20 MSTORE DUP2 PUSH1 0x0 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 DUP2 DUP2 SLOAD DUP2 LT PUSH2 0x957 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 ADD PUSH1 0x0 SWAP2 POP SWAP2 POP POP SLOAD DUP2 JUMP JUMPDEST PUSH1 0x0 DUP2 DUP2 SLOAD DUP2 LT PUSH2 0x97C JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x7 MUL ADD PUSH1 0x0 SWAP2 POP SWAP1 POP DUP1 PUSH1 0x0 ADD DUP1 SLOAD PUSH2 0x99F SWAP1 PUSH2 0x1278 JUMP JUMPDEST DUP1 PUSH1 0x1F ADD PUSH1 0x20 DUP1 SWAP2 DIV MUL PUSH1 0x20 ADD PUSH1 0x40 MLOAD SWAP1 DUP2 ADD PUSH1 0x40 MSTORE DUP1 SWAP3 SWAP2 SWAP1 DUP2 DUP2 MSTORE PUSH1 0x20 ADD DUP3 DUP1 SLOAD PUSH2 0x9CB SWAP1 PUSH2 0x1278 JUMP JUMPDEST DUP1 ISZERO PUSH2 0xA18 JUMPI DUP1 PUSH1 0x1F LT PUSH2 0x9ED JUMPI PUSH2 0x100 DUP1 DUP4 SLOAD DIV MUL DUP4 MSTORE SWAP2 PUSH1 0x20 ADD SWAP2 PUSH2 0xA18 JUMP JUMPDEST DUP3 ADD SWAP2 SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 JUMPDEST DUP2 SLOAD DUP2 MSTORE SWAP1 PUSH1 0x1 ADD SWAP1 PUSH1 0x20 ADD DUP1 DUP4 GT PUSH2 0x9FB JUMPI DUP3 SWAP1 SUB PUSH1 0x1F AND DUP3 ADD SWAP2 JUMPDEST POP POP POP POP PUSH1 0x5 DUP4 ADD SLOAD PUSH1 0x6 SWAP1 SWAP4 ADD SLOAD SWAP2 SWAP3 PUSH1 0xFF DUP2 AND SWAP3 PUSH2 0x100 SWAP1 SWAP2 DIV PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND SWAP2 POP DUP5 JUMP JUMPDEST PUSH1 0x0 SLOAD DUP3 SWAP1 DUP2 LT PUSH2 0xA65 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH2 0x1E2 SWAP1 PUSH2 0x1235 JUMP JUMPDEST DUP3 PUSH1 0x0 DUP2 DUP2 SLOAD DUP2 LT PUSH2 0xA79 JUMPI PUSH2 0xA79 PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 SWAP1 SWAP2 KECCAK256 PUSH1 0x5 PUSH1 0x7 SWAP1 SWAP3 MUL ADD ADD SLOAD PUSH1 0xFF AND PUSH2 0xAD1 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x12 PUSH1 0x24 DUP3 ADD MSTORE PUSH18 0x506F6C6C206973206E6F7420616374697665 PUSH1 0x70 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x1E2 JUMP JUMPDEST DUP4 PUSH1 0x0 DUP2 DUP2 SLOAD DUP2 LT PUSH2 0xAE5 JUMPI PUSH2 0xAE5 PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 DUP1 DUP4 KECCAK256 CALLER DUP5 MSTORE PUSH1 0x3 PUSH1 0x7 SWAP1 SWAP4 MUL ADD SWAP2 SWAP1 SWAP2 ADD SWAP1 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD PUSH1 0xFF AND ISZERO PUSH2 0xB45 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0xD PUSH1 0x24 DUP3 ADD MSTORE PUSH13 0x105B1C9958591E481D9BDD1959 PUSH1 0x9A SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x1E2 JUMP JUMPDEST DUP5 DUP5 PUSH1 0x0 DUP3 DUP2 SLOAD DUP2 LT PUSH2 0xB5A JUMPI PUSH2 0xB5A PUSH2 0x1262 JUMP JUMPDEST SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x7 MUL ADD PUSH1 0x1 ADD DUP1 SLOAD SWAP1 POP DUP2 LT PUSH2 0xBAE JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0xE PUSH1 0x24 DUP3 ADD MSTORE PUSH14 0x24B73B30B634B21037B83A34B7B7 PUSH1 0x91 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x1E2 JUMP JUMPDEST PUSH1 0x0 DUP1 DUP9 DUP2 SLOAD DUP2 LT PUSH2 0xBC2 JUMPI PUSH2 0xBC2 PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 DUP1 DUP4 KECCAK256 CALLER DUP5 MSTORE PUSH1 0x3 PUSH1 0x7 SWAP1 SWAP4 MUL ADD SWAP2 DUP3 ADD DUP2 MSTORE PUSH1 0x40 DUP1 DUP5 KECCAK256 DUP1 SLOAD PUSH1 0xFF NOT AND PUSH1 0x1 OR SWAP1 SSTORE PUSH1 0x4 DUP4 ADD SWAP1 SWAP2 MSTORE SWAP1 SWAP2 KECCAK256 DUP9 SWAP1 SSTORE PUSH1 0x2 DUP2 ADD DUP1 SLOAD SWAP2 SWAP3 POP SWAP1 DUP9 SWAP1 DUP2 LT PUSH2 0xC13 JUMPI PUSH2 0xC13 PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 DUP3 KECCAK256 ADD DUP1 SLOAD SWAP2 PUSH2 0xC29 DUP4 PUSH2 0x12B2 JUMP JUMPDEST SWAP1 SWAP2 SSTORE POP POP PUSH1 0x40 MLOAD DUP8 DUP2 MSTORE CALLER SWAP1 DUP10 SWAP1 PUSH32 0x2ACCE567DECA3AABF56327ADBB4524BD5318936EAEFA69E3A5208FFDA0CFEC09 SWAP1 PUSH1 0x20 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG3 POP POP POP POP POP POP POP POP JUMP JUMPDEST PUSH1 0x0 DUP1 SLOAD DUP4 SWAP1 DUP2 LT PUSH2 0xC93 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH2 0x1E2 SWAP1 PUSH2 0x1235 JUMP JUMPDEST PUSH1 0x0 DUP5 DUP2 SLOAD DUP2 LT PUSH2 0xCA6 JUMPI PUSH2 0xCA6 PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 DUP1 DUP4 KECCAK256 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP8 AND DUP5 MSTORE PUSH1 0x3 PUSH1 0x7 SWAP1 SWAP4 MUL ADD SWAP2 SWAP1 SWAP2 ADD SWAP1 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD PUSH1 0xFF AND SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP2 AND PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH1 0x2 PUSH1 0x20 SWAP1 DUP2 MSTORE PUSH1 0x40 SWAP2 DUP3 SWAP1 KECCAK256 DUP1 SLOAD DUP4 MLOAD DUP2 DUP5 MUL DUP2 ADD DUP5 ADD SWAP1 SWAP5 MSTORE DUP1 DUP5 MSTORE PUSH1 0x60 SWAP4 SWAP3 DUP4 ADD DUP3 DUP3 DUP1 ISZERO PUSH2 0xD3B JUMPI PUSH1 0x20 MUL DUP3 ADD SWAP2 SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 JUMPDEST DUP2 SLOAD DUP2 MSTORE PUSH1 0x20 ADD SWAP1 PUSH1 0x1 ADD SWAP1 DUP1 DUP4 GT PUSH2 0xD27 JUMPI JUMPDEST POP POP POP POP POP SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST DUP3 DUP1 SLOAD DUP3 DUP3 SSTORE SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 DUP2 ADD SWAP3 DUP3 ISZERO PUSH2 0xD8D JUMPI SWAP2 PUSH1 0x20 MUL DUP3 ADD JUMPDEST DUP3 DUP2 GT ISZERO PUSH2 0xD8D JUMPI DUP3 MLOAD DUP3 SWAP1 PUSH2 0xD7D SWAP1 DUP3 PUSH2 0x1328 JUMP JUMPDEST POP SWAP2 PUSH1 0x20 ADD SWAP2 SWAP1 PUSH1 0x1 ADD SWAP1 PUSH2 0xD67 JUMP JUMPDEST POP PUSH2 0xD99 SWAP3 SWAP2 POP PUSH2 0xDE4 JUMP JUMPDEST POP SWAP1 JUMP JUMPDEST DUP3 DUP1 SLOAD DUP3 DUP3 SSTORE SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 DUP2 ADD SWAP3 DUP3 ISZERO PUSH2 0xDD8 JUMPI SWAP2 PUSH1 0x20 MUL DUP3 ADD JUMPDEST DUP3 DUP2 GT ISZERO PUSH2 0xDD8 JUMPI DUP3 MLOAD DUP3 SSTORE SWAP2 PUSH1 0x20 ADD SWAP2 SWAP1 PUSH1 0x1 ADD SWAP1 PUSH2 0xDBD JUMP JUMPDEST POP PUSH2 0xD99 SWAP3 SWAP2 POP PUSH2 0xE01 JUMP JUMPDEST DUP1 DUP3 GT ISZERO PUSH2 0xD99 JUMPI PUSH1 0x0 PUSH2 0xDF8 DUP3 DUP3 PUSH2 0xE16 JUMP JUMPDEST POP PUSH1 0x1 ADD PUSH2 0xDE4 JUMP JUMPDEST JUMPDEST DUP1 DUP3 GT ISZERO PUSH2 0xD99 JUMPI PUSH1 0x0 DUP2 SSTORE PUSH1 0x1 ADD PUSH2 0xE02 JUMP JUMPDEST POP DUP1 SLOAD PUSH2 0xE22 SWAP1 PUSH2 0x1278 JUMP JUMPDEST PUSH1 0x0 DUP3 SSTORE DUP1 PUSH1 0x1F LT PUSH2 0xE32 JUMPI POP POP JUMP JUMPDEST PUSH1 0x1F ADD PUSH1 0x20 SWAP1 DIV SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 DUP2 ADD SWAP1 PUSH2 0xE50 SWAP2 SWAP1 PUSH2 0xE01 JUMP JUMPDEST POP JUMP JUMPDEST DUP1 CALLDATALOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP2 AND DUP2 EQ PUSH2 0xE6A JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP1 PUSH1 0x40 DUP4 DUP6 SUB SLT ISZERO PUSH2 0xE82 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP3 CALLDATALOAD SWAP2 POP PUSH2 0xE92 PUSH1 0x20 DUP5 ADD PUSH2 0xE53 JUMP JUMPDEST SWAP1 POP SWAP3 POP SWAP3 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0xEAD JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP CALLDATALOAD SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP2 MLOAD DUP1 DUP5 MSTORE PUSH1 0x0 JUMPDEST DUP2 DUP2 LT ISZERO PUSH2 0xEDA JUMPI PUSH1 0x20 DUP2 DUP6 ADD DUP2 ADD MLOAD DUP7 DUP4 ADD DUP3 ADD MSTORE ADD PUSH2 0xEBE JUMP JUMPDEST POP PUSH1 0x0 PUSH1 0x20 DUP3 DUP7 ADD ADD MSTORE PUSH1 0x20 PUSH1 0x1F NOT PUSH1 0x1F DUP4 ADD AND DUP6 ADD ADD SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 DUP2 MLOAD DUP1 DUP5 MSTORE PUSH1 0x20 DUP1 DUP6 ADD SWAP5 POP DUP1 DUP5 ADD PUSH1 0x0 JUMPDEST DUP4 DUP2 LT ISZERO PUSH2 0xF2A JUMPI DUP2 MLOAD DUP8 MSTORE SWAP6 DUP3 ADD SWAP6 SWAP1 DUP3 ADD SWAP1 PUSH1 0x1 ADD PUSH2 0xF0E JUMP JUMPDEST POP SWAP5 SWAP6 SWAP5 POP POP POP POP POP JUMP JUMPDEST PUSH1 0xC0 DUP2 MSTORE PUSH1 0x0 PUSH2 0xF48 PUSH1 0xC0 DUP4 ADD DUP10 PUSH2 0xEB4 JUMP JUMPDEST PUSH1 0x20 DUP4 DUP3 SUB DUP2 DUP6 ADD MSTORE DUP2 DUP10 MLOAD DUP1 DUP5 MSTORE DUP3 DUP5 ADD SWAP2 POP DUP3 DUP2 PUSH1 0x5 SHL DUP6 ADD ADD DUP4 DUP13 ADD PUSH1 0x0 JUMPDEST DUP4 DUP2 LT ISZERO PUSH2 0xF98 JUMPI PUSH1 0x1F NOT DUP8 DUP5 SUB ADD DUP6 MSTORE PUSH2 0xF86 DUP4 DUP4 MLOAD PUSH2 0xEB4 JUMP JUMPDEST SWAP5 DUP7 ADD SWAP5 SWAP3 POP SWAP1 DUP6 ADD SWAP1 PUSH1 0x1 ADD PUSH2 0xF6A JUMP JUMPDEST POP POP DUP7 DUP2 SUB PUSH1 0x40 DUP9 ADD MSTORE PUSH2 0xFAC DUP2 DUP13 PUSH2 0xEFA JUMP JUMPDEST SWAP6 POP POP POP POP POP POP PUSH2 0xFC1 PUSH1 0x60 DUP4 ADD DUP7 ISZERO ISZERO SWAP1 MSTORE JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP4 SWAP1 SWAP4 AND PUSH1 0x80 DUP3 ADD MSTORE PUSH1 0xA0 ADD MSTORE SWAP5 SWAP4 POP POP POP POP JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x41 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH1 0x1F DUP3 ADD PUSH1 0x1F NOT AND DUP2 ADD PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT DUP3 DUP3 LT OR ISZERO PUSH2 0x101D JUMPI PUSH2 0x101D PUSH2 0xFDE JUMP JUMPDEST PUSH1 0x40 MSTORE SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP3 PUSH1 0x1F DUP4 ADD SLT PUSH2 0x1036 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP2 CALLDATALOAD PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT ISZERO PUSH2 0x1050 JUMPI PUSH2 0x1050 PUSH2 0xFDE JUMP JUMPDEST PUSH2 0x1063 PUSH1 0x1F DUP3 ADD PUSH1 0x1F NOT AND PUSH1 0x20 ADD PUSH2 0xFF4 JUMP JUMPDEST DUP2 DUP2 MSTORE DUP5 PUSH1 0x20 DUP4 DUP7 ADD ADD GT ISZERO PUSH2 0x1078 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP2 PUSH1 0x20 DUP6 ADD PUSH1 0x20 DUP4 ADD CALLDATACOPY PUSH1 0x0 SWAP2 DUP2 ADD PUSH1 0x20 ADD SWAP2 SWAP1 SWAP2 MSTORE SWAP4 SWAP3 POP POP POP JUMP JUMPDEST PUSH1 0x0 DUP1 PUSH1 0x40 DUP4 DUP6 SUB SLT ISZERO PUSH2 0x10A8 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP3 CALLDATALOAD PUSH8 0xFFFFFFFFFFFFFFFF DUP1 DUP3 GT ISZERO PUSH2 0x10C0 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH2 0x10CC DUP7 DUP4 DUP8 ADD PUSH2 0x1025 JUMP JUMPDEST SWAP4 POP PUSH1 0x20 SWAP2 POP DUP2 DUP6 ADD CALLDATALOAD DUP2 DUP2 GT ISZERO PUSH2 0x10E3 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP6 ADD PUSH1 0x1F DUP2 ADD DUP8 SGT PUSH2 0x10F4 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP1 CALLDATALOAD DUP3 DUP2 GT ISZERO PUSH2 0x1106 JUMPI PUSH2 0x1106 PUSH2 0xFDE JUMP JUMPDEST DUP1 PUSH1 0x5 SHL PUSH2 0x1115 DUP6 DUP3 ADD PUSH2 0xFF4 JUMP JUMPDEST SWAP2 DUP3 MSTORE DUP3 DUP2 ADD DUP6 ADD SWAP2 DUP6 DUP2 ADD SWAP1 DUP11 DUP5 GT ISZERO PUSH2 0x112F JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP7 DUP6 ADD SWAP3 POP JUMPDEST DUP4 DUP4 LT ISZERO PUSH2 0x116B JUMPI DUP3 CALLDATALOAD DUP7 DUP2 GT ISZERO PUSH2 0x114D JUMPI PUSH1 0x0 DUP1 DUP2 REVERT JUMPDEST PUSH2 0x115B DUP13 DUP10 DUP4 DUP10 ADD ADD PUSH2 0x1025 JUMP JUMPDEST DUP4 MSTORE POP SWAP2 DUP7 ADD SWAP2 SWAP1 DUP7 ADD SWAP1 PUSH2 0x1135 JUMP JUMPDEST DUP1 SWAP8 POP POP POP POP POP POP POP POP SWAP3 POP SWAP3 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP1 PUSH1 0x40 DUP4 DUP6 SUB SLT ISZERO PUSH2 0x118F JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH2 0x1198 DUP4 PUSH2 0xE53 JUMP JUMPDEST SWAP5 PUSH1 0x20 SWAP4 SWAP1 SWAP4 ADD CALLDATALOAD SWAP4 POP POP POP JUMP JUMPDEST PUSH1 0x80 DUP2 MSTORE PUSH1 0x0 PUSH2 0x11B9 PUSH1 0x80 DUP4 ADD DUP8 PUSH2 0xEB4 JUMP JUMPDEST SWAP5 ISZERO ISZERO PUSH1 0x20 DUP4 ADD MSTORE POP PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP3 SWAP1 SWAP3 AND PUSH1 0x40 DUP4 ADD MSTORE PUSH1 0x60 SWAP1 SWAP2 ADD MSTORE SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP1 PUSH1 0x40 DUP4 DUP6 SUB SLT ISZERO PUSH2 0x11F1 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP POP DUP1 CALLDATALOAD SWAP3 PUSH1 0x20 SWAP1 SWAP2 ADD CALLDATALOAD SWAP2 POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0x1212 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH2 0x121B DUP3 PUSH2 0xE53 JUMP JUMPDEST SWAP4 SWAP3 POP POP POP JUMP JUMPDEST PUSH1 0x20 DUP2 MSTORE PUSH1 0x0 PUSH2 0x121B PUSH1 0x20 DUP4 ADD DUP5 PUSH2 0xEFA JUMP JUMPDEST PUSH1 0x20 DUP1 DUP3 MSTORE PUSH1 0x13 SWAP1 DUP3 ADD MSTORE PUSH19 0x141BDB1B08191BD95CC81B9BDD08195E1A5CDD PUSH1 0x6A SHL PUSH1 0x40 DUP3 ADD MSTORE PUSH1 0x60 ADD SWAP1 JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x32 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH1 0x1 DUP2 DUP2 SHR SWAP1 DUP3 AND DUP1 PUSH2 0x128C JUMPI PUSH1 0x7F DUP3 AND SWAP2 POP JUMPDEST PUSH1 0x20 DUP3 LT DUP2 SUB PUSH2 0x12AC JUMPI PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x22 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x1 DUP3 ADD PUSH2 0x12D2 JUMPI PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST POP PUSH1 0x1 ADD SWAP1 JUMP JUMPDEST PUSH1 0x1F DUP3 GT ISZERO PUSH2 0x1323 JUMPI PUSH1 0x0 DUP2 DUP2 MSTORE PUSH1 0x20 DUP2 KECCAK256 PUSH1 0x1F DUP6 ADD PUSH1 0x5 SHR DUP2 ADD PUSH1 0x20 DUP7 LT ISZERO PUSH2 0x1300 JUMPI POP DUP1 JUMPDEST PUSH1 0x1F DUP6 ADD PUSH1 0x5 SHR DUP3 ADD SWAP2 POP JUMPDEST DUP2 DUP2 LT ISZERO PUSH2 0x131F JUMPI DUP3 DUP2 SSTORE PUSH1 0x1 ADD PUSH2 0x130C JUMP JUMPDEST POP POP POP JUMPDEST POP POP POP JUMP JUMPDEST DUP2 MLOAD PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT ISZERO PUSH2 0x1342 JUMPI PUSH2 0x1342 PUSH2 0xFDE JUMP JUMPDEST PUSH2 0x1356 DUP2 PUSH2 0x1350 DUP5 SLOAD PUSH2 0x1278 JUMP JUMPDEST DUP5 PUSH2 0x12D9 JUMP JUMPDEST PUSH1 0x20 DUP1 PUSH1 0x1F DUP4 GT PUSH1 0x1 DUP2 EQ PUSH2 0x138B JUMPI PUSH1 0x0 DUP5 ISZERO PUSH2 0x1373 JUMPI POP DUP6 DUP4 ADD MLOAD JUMPDEST PUSH1 0x0 NOT PUSH1 0x3 DUP7 SWAP1 SHL SHR NOT AND PUSH1 0x1 DUP6 SWAP1 SHL OR DUP6 SSTORE PUSH2 0x131F JUMP JUMPDEST PUSH1 0x0 DUP6 DUP2 MSTORE PUSH1 0x20 DUP2 KECCAK256 PUSH1 0x1F NOT DUP7 AND SWAP2 JUMPDEST DUP3 DUP2 LT ISZERO PUSH2 0x13BA JUMPI DUP9 DUP7 ADD MLOAD DUP3 SSTORE SWAP5 DUP5 ADD SWAP5 PUSH1 0x1 SWAP1 SWAP2 ADD SWAP1 DUP5 ADD PUSH2 0x139B JUMP JUMPDEST POP DUP6 DUP3 LT ISZERO PUSH2 0x13D8 JUMPI DUP8 DUP6 ADD MLOAD PUSH1 0x0 NOT PUSH1 0x3 DUP9 SWAP1 SHL PUSH1 0xF8 AND SHR NOT AND DUP2 SSTORE JUMPDEST POP POP POP POP POP PUSH1 0x1 SWAP1 DUP2 SHL ADD SWAP1 SSTORE POP JUMP JUMPDEST PUSH1 0x40 DUP2 MSTORE PUSH1 0x0 PUSH2 0x13FB PUSH1 0x40 DUP4 ADD DUP6 PUSH2 0xEB4 JUMP JUMPDEST SWAP1 POP DUP3 PUSH1 0x20 DUP4 ADD MSTORE SWAP4 SWAP3 POP POP POP JUMP INVALID LOG2 PUSH5 0x6970667358 0x22 SLT KECCAK256 CALLDATALOAD 0x29 0x22 JUMPI SWAP14 SELFDESTRUCT BALANCE 0x2E PUSH26 0xF227E53AB804CA115CB3517539EE26DDC29265347D099A64736F PUSH13 0x63430008130033000000000000 ", "sourceMap": "159:6101:0:-:0;;;;;;;;;;;;;;;;;;;"}, "deployedBytecode": {"functionDebugData": {"@createPoll_283": {"entryPoint": 1658, "id": 283, "parameterSlots": 2, "returnSlots": 1}, "@creatorPolls_36": {"entryPoint": 2363, "id": 36, "parameterSlots": 0, "returnSlots": 0}, "@getCreatorPolls_505": {"entryPoint": 3291, "id": 505, "parameterSlots": 1, "returnSlots": 1}, "@getPollCount_399": {"entryPoint": null, "id": 399, "parameterSlots": 0, "returnSlots": 1}, "@getPoll_389": {"entryPoint": 688, "id": 389, "parameterSlots": 1, "returnSlots": 6}, "@getUserVote_451": {"entryPoint": 446, "id": 451, "parameterSlots": 2, "returnSlots": 1}, "@hasUserVoted_420": {"entryPoint": 3183, "id": 420, "parameterSlots": 2, "returnSlots": 1}, "@pollCount_31": {"entryPoint": null, "id": 31, "parameterSlots": 0, "returnSlots": 0}, "@polls_29": {"entryPoint": 2412, "id": 29, "parameterSlots": 0, "returnSlots": 0}, "@togglePollStatus_491": {"entryPoint": 1274, "id": 491, "parameterSlots": 1, "returnSlots": 0}, "@vote_344": {"entryPoint": 2626, "id": 344, "parameterSlots": 2, "returnSlots": 0}, "abi_decode_address": {"entryPoint": 3667, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_decode_string": {"entryPoint": 4133, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_decode_tuple_t_address": {"entryPoint": 4608, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_decode_tuple_t_addresst_uint256": {"entryPoint": 4476, "id": null, "parameterSlots": 2, "returnSlots": 2}, "abi_decode_tuple_t_string_memory_ptrt_array$_t_string_memory_ptr_$dyn_memory_ptr": {"entryPoint": 4245, "id": null, "parameterSlots": 2, "returnSlots": 2}, "abi_decode_tuple_t_uint256": {"entryPoint": 3739, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_decode_tuple_t_uint256t_address": {"entryPoint": 3695, "id": null, "parameterSlots": 2, "returnSlots": 2}, "abi_decode_tuple_t_uint256t_uint256": {"entryPoint": 4574, "id": null, "parameterSlots": 2, "returnSlots": 2}, "abi_encode_address": {"entryPoint": null, "id": null, "parameterSlots": 2, "returnSlots": 0}, "abi_encode_array_uint256_dyn": {"entryPoint": 3834, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_bool": {"entryPoint": null, "id": null, "parameterSlots": 2, "returnSlots": 0}, "abi_encode_string": {"entryPoint": 3764, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_tuple_t_array$_t_uint256_$dyn_memory_ptr__to_t_array$_t_uint256_$dyn_memory_ptr__fromStack_reversed": {"entryPoint": 4642, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_tuple_t_bool__to_t_bool__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_tuple_t_string_memory_ptr_t_array$_t_string_memory_ptr_$dyn_memory_ptr_t_array$_t_uint256_$dyn_memory_ptr_t_bool_t_address_t_uint256__to_t_string_memory_ptr_t_array$_t_string_memory_ptr_$dyn_memory_ptr_t_array$_t_uint256_$dyn_memory_ptr_t_bool_t_address_t_uint256__fromStack_reversed": {"entryPoint": 3893, "id": null, "parameterSlots": 7, "returnSlots": 1}, "abi_encode_tuple_t_string_memory_ptr_t_bool_t_address_t_uint256__to_t_string_memory_ptr_t_bool_t_address_t_uint256__fromStack_reversed": {"entryPoint": 4518, "id": null, "parameterSlots": 5, "returnSlots": 1}, "abi_encode_tuple_t_string_memory_ptr_t_uint256__to_t_string_memory_ptr_t_uint256__fromStack_reversed": {"entryPoint": 5096, "id": null, "parameterSlots": 3, "returnSlots": 1}, "abi_encode_tuple_t_stringliteral_39d63f22c983e0a835c80b26c00e97ac46e50731b96804551d851bf715b5fd2c__to_t_string_memory_ptr__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_encode_tuple_t_stringliteral_47303e9cecb0d187ef38fc0ef78d94b0ad3bfd977aa49a6ef487d19e96bc3077__to_t_string_memory_ptr__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_encode_tuple_t_stringliteral_512fc59044d4f0722f9346c450973ffe8aac7aa1142e536739987018593c53b6__to_t_string_memory_ptr__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_encode_tuple_t_stringliteral_85be9a3a8b38ddaef4e16dab673e124fe2fa73224e04bf43989f8bc735b45740__to_t_string_memory_ptr__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_encode_tuple_t_stringliteral_8df94984f0986c4b0f652788cbe872845edfb9f154cbd9bb376c68bda91b6cbd__to_t_string_memory_ptr__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_encode_tuple_t_stringliteral_a570800e348d215f0f8e7aaa1afa84c103bca1d27fa74d7cf9c25b83f991280b__to_t_string_memory_ptr__fromStack_reversed": {"entryPoint": 4661, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_encode_tuple_t_stringliteral_b5c2a862288076e78ffb9f7b2c2d4242ebde1778466579712ffc2d1471c123ec__to_t_string_memory_ptr__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_encode_tuple_t_stringliteral_e91f32e8485e8cc61ee75db81581675d5614973e773b4b2d6682176c252ce441__to_t_string_memory_ptr__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_encode_tuple_t_stringliteral_e951eb8529546f8abd8d997e0753f069f65b139bcb89559e3d3f221bef3b55a5__to_t_string_memory_ptr__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_encode_tuple_t_stringliteral_ec1b155bf1042601d514070206494c49df2fdba4a1da92fad635a0f6e770613f__to_t_string_memory_ptr__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 2, "returnSlots": 1}, "allocate_memory": {"entryPoint": 4084, "id": null, "parameterSlots": 1, "returnSlots": 1}, "array_dataslot_string_storage": {"entryPoint": null, "id": null, "parameterSlots": 1, "returnSlots": 1}, "clean_up_bytearray_end_slots_string_storage": {"entryPoint": 4825, "id": null, "parameterSlots": 3, "returnSlots": 0}, "copy_byte_array_to_storage_from_t_string_memory_ptr_to_t_string_storage": {"entryPoint": 4904, "id": null, "parameterSlots": 2, "returnSlots": 0}, "extract_byte_array_length": {"entryPoint": 4728, "id": null, "parameterSlots": 1, "returnSlots": 1}, "extract_used_part_and_set_length_of_short_byte_array": {"entryPoint": null, "id": null, "parameterSlots": 2, "returnSlots": 1}, "increment_t_uint256": {"entryPoint": 4786, "id": null, "parameterSlots": 1, "returnSlots": 1}, "panic_error_0x32": {"entryPoint": 4706, "id": null, "parameterSlots": 0, "returnSlots": 0}, "panic_error_0x41": {"entryPoint": 4062, "id": null, "parameterSlots": 0, "returnSlots": 0}}, "generatedSources": [{"ast": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "0:13930:1", "statements": [{"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6:3:1", "statements": []}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "63:124:1", "statements": [{"nodeType": "YulAssignment", "src": "73:29:1", "value": {"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "95:6:1"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "82:12:1"}, "nodeType": "YulFunctionCall", "src": "82:20:1"}, "variableNames": [{"name": "value", "nodeType": "YulIdentifier", "src": "73:5:1"}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "165:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "174:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "177:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "167:6:1"}, "nodeType": "YulFunctionCall", "src": "167:12:1"}, "nodeType": "YulExpressionStatement", "src": "167:12:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "124:5:1"}, {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "135:5:1"}, {"arguments": [{"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "150:3:1", "type": "", "value": "160"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "155:1:1", "type": "", "value": "1"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "146:3:1"}, "nodeType": "YulFunctionCall", "src": "146:11:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "159:1:1", "type": "", "value": "1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "142:3:1"}, "nodeType": "YulFunctionCall", "src": "142:19:1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "131:3:1"}, "nodeType": "YulFunctionCall", "src": "131:31:1"}], "functionName": {"name": "eq", "nodeType": "YulIdentifier", "src": "121:2:1"}, "nodeType": "YulFunctionCall", "src": "121:42:1"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "114:6:1"}, "nodeType": "YulFunctionCall", "src": "114:50:1"}, "nodeType": "YulIf", "src": "111:70:1"}]}, "name": "abi_decode_address", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "offset", "nodeType": "YulTypedName", "src": "42:6:1", "type": ""}], "returnVariables": [{"name": "value", "nodeType": "YulTypedName", "src": "53:5:1", "type": ""}], "src": "14:173:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "279:167:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "325:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "334:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "337:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "327:6:1"}, "nodeType": "YulFunctionCall", "src": "327:12:1"}, "nodeType": "YulExpressionStatement", "src": "327:12:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nodeType": "YulIdentifier", "src": "300:7:1"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "309:9:1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "296:3:1"}, "nodeType": "YulFunctionCall", "src": "296:23:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "321:2:1", "type": "", "value": "64"}], "functionName": {"name": "slt", "nodeType": "YulIdentifier", "src": "292:3:1"}, "nodeType": "YulFunctionCall", "src": "292:32:1"}, "nodeType": "YulIf", "src": "289:52:1"}, {"nodeType": "YulAssignment", "src": "350:33:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "373:9:1"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "360:12:1"}, "nodeType": "YulFunctionCall", "src": "360:23:1"}, "variableNames": [{"name": "value0", "nodeType": "YulIdentifier", "src": "350:6:1"}]}, {"nodeType": "YulAssignment", "src": "392:48:1", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "425:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "436:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "421:3:1"}, "nodeType": "YulFunctionCall", "src": "421:18:1"}], "functionName": {"name": "abi_decode_address", "nodeType": "YulIdentifier", "src": "402:18:1"}, "nodeType": "YulFunctionCall", "src": "402:38:1"}, "variableNames": [{"name": "value1", "nodeType": "YulIdentifier", "src": "392:6:1"}]}]}, "name": "abi_decode_tuple_t_uint256t_address", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "237:9:1", "type": ""}, {"name": "dataEnd", "nodeType": "YulTypedName", "src": "248:7:1", "type": ""}], "returnVariables": [{"name": "value0", "nodeType": "YulTypedName", "src": "260:6:1", "type": ""}, {"name": "value1", "nodeType": "YulTypedName", "src": "268:6:1", "type": ""}], "src": "192:254:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "552:76:1", "statements": [{"nodeType": "YulAssignment", "src": "562:26:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "574:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "585:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "570:3:1"}, "nodeType": "YulFunctionCall", "src": "570:18:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "562:4:1"}]}, {"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "604:9:1"}, {"name": "value0", "nodeType": "YulIdentifier", "src": "615:6:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "597:6:1"}, "nodeType": "YulFunctionCall", "src": "597:25:1"}, "nodeType": "YulExpressionStatement", "src": "597:25:1"}]}, "name": "abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "521:9:1", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "532:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "543:4:1", "type": ""}], "src": "451:177:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "703:110:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "749:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "758:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "761:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "751:6:1"}, "nodeType": "YulFunctionCall", "src": "751:12:1"}, "nodeType": "YulExpressionStatement", "src": "751:12:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nodeType": "YulIdentifier", "src": "724:7:1"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "733:9:1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "720:3:1"}, "nodeType": "YulFunctionCall", "src": "720:23:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "745:2:1", "type": "", "value": "32"}], "functionName": {"name": "slt", "nodeType": "YulIdentifier", "src": "716:3:1"}, "nodeType": "YulFunctionCall", "src": "716:32:1"}, "nodeType": "YulIf", "src": "713:52:1"}, {"nodeType": "YulAssignment", "src": "774:33:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "797:9:1"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "784:12:1"}, "nodeType": "YulFunctionCall", "src": "784:23:1"}, "variableNames": [{"name": "value0", "nodeType": "YulIdentifier", "src": "774:6:1"}]}]}, "name": "abi_decode_tuple_t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "669:9:1", "type": ""}, {"name": "dataEnd", "nodeType": "YulTypedName", "src": "680:7:1", "type": ""}], "returnVariables": [{"name": "value0", "nodeType": "YulTypedName", "src": "692:6:1", "type": ""}], "src": "633:180:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "868:373:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "878:26:1", "value": {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "898:5:1"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "892:5:1"}, "nodeType": "YulFunctionCall", "src": "892:12:1"}, "variables": [{"name": "length", "nodeType": "YulTypedName", "src": "882:6:1", "type": ""}]}, {"expression": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "920:3:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "925:6:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "913:6:1"}, "nodeType": "YulFunctionCall", "src": "913:19:1"}, "nodeType": "YulExpressionStatement", "src": "913:19:1"}, {"nodeType": "YulVariableDeclaration", "src": "941:10:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "950:1:1", "type": "", "value": "0"}, "variables": [{"name": "i", "nodeType": "YulTypedName", "src": "945:1:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1012:110:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "1026:14:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1036:4:1", "type": "", "value": "0x20"}, "variables": [{"name": "_1", "nodeType": "YulTypedName", "src": "1030:2:1", "type": ""}]}, {"expression": {"arguments": [{"arguments": [{"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "1068:3:1"}, {"name": "i", "nodeType": "YulIdentifier", "src": "1073:1:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1064:3:1"}, "nodeType": "YulFunctionCall", "src": "1064:11:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "1077:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1060:3:1"}, "nodeType": "YulFunctionCall", "src": "1060:20:1"}, {"arguments": [{"arguments": [{"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "1096:5:1"}, {"name": "i", "nodeType": "YulIdentifier", "src": "1103:1:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1092:3:1"}, "nodeType": "YulFunctionCall", "src": "1092:13:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "1107:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1088:3:1"}, "nodeType": "YulFunctionCall", "src": "1088:22:1"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "1082:5:1"}, "nodeType": "YulFunctionCall", "src": "1082:29:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1053:6:1"}, "nodeType": "YulFunctionCall", "src": "1053:59:1"}, "nodeType": "YulExpressionStatement", "src": "1053:59:1"}]}, "condition": {"arguments": [{"name": "i", "nodeType": "YulIdentifier", "src": "971:1:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "974:6:1"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "968:2:1"}, "nodeType": "YulFunctionCall", "src": "968:13:1"}, "nodeType": "YulForLoop", "post": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "982:21:1", "statements": [{"nodeType": "YulAssignment", "src": "984:17:1", "value": {"arguments": [{"name": "i", "nodeType": "YulIdentifier", "src": "993:1:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "996:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "989:3:1"}, "nodeType": "YulFunctionCall", "src": "989:12:1"}, "variableNames": [{"name": "i", "nodeType": "YulIdentifier", "src": "984:1:1"}]}]}, "pre": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "964:3:1", "statements": []}, "src": "960:162:1"}, {"expression": {"arguments": [{"arguments": [{"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "1146:3:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "1151:6:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1142:3:1"}, "nodeType": "YulFunctionCall", "src": "1142:16:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1160:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1138:3:1"}, "nodeType": "YulFunctionCall", "src": "1138:27:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1167:1:1", "type": "", "value": "0"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1131:6:1"}, "nodeType": "YulFunctionCall", "src": "1131:38:1"}, "nodeType": "YulExpressionStatement", "src": "1131:38:1"}, {"nodeType": "YulAssignment", "src": "1178:57:1", "value": {"arguments": [{"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "1193:3:1"}, {"arguments": [{"arguments": [{"name": "length", "nodeType": "YulIdentifier", "src": "1206:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1214:2:1", "type": "", "value": "31"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1202:3:1"}, "nodeType": "YulFunctionCall", "src": "1202:15:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1223:2:1", "type": "", "value": "31"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "1219:3:1"}, "nodeType": "YulFunctionCall", "src": "1219:7:1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "1198:3:1"}, "nodeType": "YulFunctionCall", "src": "1198:29:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1189:3:1"}, "nodeType": "YulFunctionCall", "src": "1189:39:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1230:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1185:3:1"}, "nodeType": "YulFunctionCall", "src": "1185:50:1"}, "variableNames": [{"name": "end", "nodeType": "YulIdentifier", "src": "1178:3:1"}]}]}, "name": "abi_encode_string", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "845:5:1", "type": ""}, {"name": "pos", "nodeType": "YulTypedName", "src": "852:3:1", "type": ""}], "returnVariables": [{"name": "end", "nodeType": "YulTypedName", "src": "860:3:1", "type": ""}], "src": "818:423:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1307:374:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "1317:26:1", "value": {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "1337:5:1"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "1331:5:1"}, "nodeType": "YulFunctionCall", "src": "1331:12:1"}, "variables": [{"name": "length", "nodeType": "YulTypedName", "src": "1321:6:1", "type": ""}]}, {"expression": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "1359:3:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "1364:6:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1352:6:1"}, "nodeType": "YulFunctionCall", "src": "1352:19:1"}, "nodeType": "YulExpressionStatement", "src": "1352:19:1"}, {"nodeType": "YulVariableDeclaration", "src": "1380:14:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1390:4:1", "type": "", "value": "0x20"}, "variables": [{"name": "_1", "nodeType": "YulTypedName", "src": "1384:2:1", "type": ""}]}, {"nodeType": "YulAssignment", "src": "1403:19:1", "value": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "1414:3:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "1419:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1410:3:1"}, "nodeType": "YulFunctionCall", "src": "1410:12:1"}, "variableNames": [{"name": "pos", "nodeType": "YulIdentifier", "src": "1403:3:1"}]}, {"nodeType": "YulVariableDeclaration", "src": "1431:28:1", "value": {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "1449:5:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "1456:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1445:3:1"}, "nodeType": "YulFunctionCall", "src": "1445:14:1"}, "variables": [{"name": "srcPtr", "nodeType": "YulTypedName", "src": "1435:6:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "1468:10:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1477:1:1", "type": "", "value": "0"}, "variables": [{"name": "i", "nodeType": "YulTypedName", "src": "1472:1:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1536:120:1", "statements": [{"expression": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "1557:3:1"}, {"arguments": [{"name": "srcPtr", "nodeType": "YulIdentifier", "src": "1568:6:1"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "1562:5:1"}, "nodeType": "YulFunctionCall", "src": "1562:13:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1550:6:1"}, "nodeType": "YulFunctionCall", "src": "1550:26:1"}, "nodeType": "YulExpressionStatement", "src": "1550:26:1"}, {"nodeType": "YulAssignment", "src": "1589:19:1", "value": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "1600:3:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "1605:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1596:3:1"}, "nodeType": "YulFunctionCall", "src": "1596:12:1"}, "variableNames": [{"name": "pos", "nodeType": "YulIdentifier", "src": "1589:3:1"}]}, {"nodeType": "YulAssignment", "src": "1621:25:1", "value": {"arguments": [{"name": "srcPtr", "nodeType": "YulIdentifier", "src": "1635:6:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "1643:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1631:3:1"}, "nodeType": "YulFunctionCall", "src": "1631:15:1"}, "variableNames": [{"name": "srcPtr", "nodeType": "YulIdentifier", "src": "1621:6:1"}]}]}, "condition": {"arguments": [{"name": "i", "nodeType": "YulIdentifier", "src": "1498:1:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "1501:6:1"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "1495:2:1"}, "nodeType": "YulFunctionCall", "src": "1495:13:1"}, "nodeType": "YulForLoop", "post": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1509:18:1", "statements": [{"nodeType": "YulAssignment", "src": "1511:14:1", "value": {"arguments": [{"name": "i", "nodeType": "YulIdentifier", "src": "1520:1:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1523:1:1", "type": "", "value": "1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1516:3:1"}, "nodeType": "YulFunctionCall", "src": "1516:9:1"}, "variableNames": [{"name": "i", "nodeType": "YulIdentifier", "src": "1511:1:1"}]}]}, "pre": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1491:3:1", "statements": []}, "src": "1487:169:1"}, {"nodeType": "YulAssignment", "src": "1665:10:1", "value": {"name": "pos", "nodeType": "YulIdentifier", "src": "1672:3:1"}, "variableNames": [{"name": "end", "nodeType": "YulIdentifier", "src": "1665:3:1"}]}]}, "name": "abi_encode_array_uint256_dyn", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "1284:5:1", "type": ""}, {"name": "pos", "nodeType": "YulTypedName", "src": "1291:3:1", "type": ""}], "returnVariables": [{"name": "end", "nodeType": "YulTypedName", "src": "1299:3:1", "type": ""}], "src": "1246:435:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1727:50:1", "statements": [{"expression": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "1744:3:1"}, {"arguments": [{"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "1763:5:1"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "1756:6:1"}, "nodeType": "YulFunctionCall", "src": "1756:13:1"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "1749:6:1"}, "nodeType": "YulFunctionCall", "src": "1749:21:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1737:6:1"}, "nodeType": "YulFunctionCall", "src": "1737:34:1"}, "nodeType": "YulExpressionStatement", "src": "1737:34:1"}]}, "name": "abi_encode_bool", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "1711:5:1", "type": ""}, {"name": "pos", "nodeType": "YulTypedName", "src": "1718:3:1", "type": ""}], "src": "1686:91:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1826:60:1", "statements": [{"expression": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "1843:3:1"}, {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "1852:5:1"}, {"arguments": [{"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1867:3:1", "type": "", "value": "160"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1872:1:1", "type": "", "value": "1"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "1863:3:1"}, "nodeType": "YulFunctionCall", "src": "1863:11:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1876:1:1", "type": "", "value": "1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "1859:3:1"}, "nodeType": "YulFunctionCall", "src": "1859:19:1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "1848:3:1"}, "nodeType": "YulFunctionCall", "src": "1848:31:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1836:6:1"}, "nodeType": "YulFunctionCall", "src": "1836:44:1"}, "nodeType": "YulExpressionStatement", "src": "1836:44:1"}]}, "name": "abi_encode_address", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "1810:5:1", "type": ""}, {"name": "pos", "nodeType": "YulTypedName", "src": "1817:3:1", "type": ""}], "src": "1782:104:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2266:960:1", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2283:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2294:3:1", "type": "", "value": "192"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2276:6:1"}, "nodeType": "YulFunctionCall", "src": "2276:22:1"}, "nodeType": "YulExpressionStatement", "src": "2276:22:1"}, {"nodeType": "YulVariableDeclaration", "src": "2307:60:1", "value": {"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "2339:6:1"}, {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2351:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2362:3:1", "type": "", "value": "192"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2347:3:1"}, "nodeType": "YulFunctionCall", "src": "2347:19:1"}], "functionName": {"name": "abi_encode_string", "nodeType": "YulIdentifier", "src": "2321:17:1"}, "nodeType": "YulFunctionCall", "src": "2321:46:1"}, "variables": [{"name": "tail_1", "nodeType": "YulTypedName", "src": "2311:6:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "2376:12:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2386:2:1", "type": "", "value": "32"}, "variables": [{"name": "_1", "nodeType": "YulTypedName", "src": "2380:2:1", "type": ""}]}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2408:9:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "2419:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2404:3:1"}, "nodeType": "YulFunctionCall", "src": "2404:18:1"}, {"arguments": [{"name": "tail_1", "nodeType": "YulIdentifier", "src": "2428:6:1"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "2436:9:1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "2424:3:1"}, "nodeType": "YulFunctionCall", "src": "2424:22:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2397:6:1"}, "nodeType": "YulFunctionCall", "src": "2397:50:1"}, "nodeType": "YulExpressionStatement", "src": "2397:50:1"}, {"nodeType": "YulVariableDeclaration", "src": "2456:17:1", "value": {"name": "tail_1", "nodeType": "YulIdentifier", "src": "2467:6:1"}, "variables": [{"name": "pos", "nodeType": "YulTypedName", "src": "2460:3:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "2482:27:1", "value": {"arguments": [{"name": "value1", "nodeType": "YulIdentifier", "src": "2502:6:1"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "2496:5:1"}, "nodeType": "YulFunctionCall", "src": "2496:13:1"}, "variables": [{"name": "length", "nodeType": "YulTypedName", "src": "2486:6:1", "type": ""}]}, {"expression": {"arguments": [{"name": "tail_1", "nodeType": "YulIdentifier", "src": "2525:6:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "2533:6:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2518:6:1"}, "nodeType": "YulFunctionCall", "src": "2518:22:1"}, "nodeType": "YulExpressionStatement", "src": "2518:22:1"}, {"nodeType": "YulAssignment", "src": "2549:22:1", "value": {"arguments": [{"name": "tail_1", "nodeType": "YulIdentifier", "src": "2560:6:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "2568:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2556:3:1"}, "nodeType": "YulFunctionCall", "src": "2556:15:1"}, "variableNames": [{"name": "pos", "nodeType": "YulIdentifier", "src": "2549:3:1"}]}, {"nodeType": "YulVariableDeclaration", "src": "2580:50:1", "value": {"arguments": [{"arguments": [{"name": "tail_1", "nodeType": "YulIdentifier", "src": "2602:6:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2614:1:1", "type": "", "value": "5"}, {"name": "length", "nodeType": "YulIdentifier", "src": "2617:6:1"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "2610:3:1"}, "nodeType": "YulFunctionCall", "src": "2610:14:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2598:3:1"}, "nodeType": "YulFunctionCall", "src": "2598:27:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "2627:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2594:3:1"}, "nodeType": "YulFunctionCall", "src": "2594:36:1"}, "variables": [{"name": "tail_2", "nodeType": "YulTypedName", "src": "2584:6:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "2639:29:1", "value": {"arguments": [{"name": "value1", "nodeType": "YulIdentifier", "src": "2657:6:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "2665:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2653:3:1"}, "nodeType": "YulFunctionCall", "src": "2653:15:1"}, "variables": [{"name": "srcPtr", "nodeType": "YulTypedName", "src": "2643:6:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "2677:10:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2686:1:1", "type": "", "value": "0"}, "variables": [{"name": "i", "nodeType": "YulTypedName", "src": "2681:1:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2745:203:1", "statements": [{"expression": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "2766:3:1"}, {"arguments": [{"arguments": [{"name": "tail_2", "nodeType": "YulIdentifier", "src": "2779:6:1"}, {"name": "tail_1", "nodeType": "YulIdentifier", "src": "2787:6:1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "2775:3:1"}, "nodeType": "YulFunctionCall", "src": "2775:19:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2800:2:1", "type": "", "value": "31"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "2796:3:1"}, "nodeType": "YulFunctionCall", "src": "2796:7:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2771:3:1"}, "nodeType": "YulFunctionCall", "src": "2771:33:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2759:6:1"}, "nodeType": "YulFunctionCall", "src": "2759:46:1"}, "nodeType": "YulExpressionStatement", "src": "2759:46:1"}, {"nodeType": "YulAssignment", "src": "2818:50:1", "value": {"arguments": [{"arguments": [{"name": "srcPtr", "nodeType": "YulIdentifier", "src": "2852:6:1"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "2846:5:1"}, "nodeType": "YulFunctionCall", "src": "2846:13:1"}, {"name": "tail_2", "nodeType": "YulIdentifier", "src": "2861:6:1"}], "functionName": {"name": "abi_encode_string", "nodeType": "YulIdentifier", "src": "2828:17:1"}, "nodeType": "YulFunctionCall", "src": "2828:40:1"}, "variableNames": [{"name": "tail_2", "nodeType": "YulIdentifier", "src": "2818:6:1"}]}, {"nodeType": "YulAssignment", "src": "2881:25:1", "value": {"arguments": [{"name": "srcPtr", "nodeType": "YulIdentifier", "src": "2895:6:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "2903:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2891:3:1"}, "nodeType": "YulFunctionCall", "src": "2891:15:1"}, "variableNames": [{"name": "srcPtr", "nodeType": "YulIdentifier", "src": "2881:6:1"}]}, {"nodeType": "YulAssignment", "src": "2919:19:1", "value": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "2930:3:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "2935:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2926:3:1"}, "nodeType": "YulFunctionCall", "src": "2926:12:1"}, "variableNames": [{"name": "pos", "nodeType": "YulIdentifier", "src": "2919:3:1"}]}]}, "condition": {"arguments": [{"name": "i", "nodeType": "YulIdentifier", "src": "2707:1:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "2710:6:1"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "2704:2:1"}, "nodeType": "YulFunctionCall", "src": "2704:13:1"}, "nodeType": "YulForLoop", "post": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2718:18:1", "statements": [{"nodeType": "YulAssignment", "src": "2720:14:1", "value": {"arguments": [{"name": "i", "nodeType": "YulIdentifier", "src": "2729:1:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2732:1:1", "type": "", "value": "1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2725:3:1"}, "nodeType": "YulFunctionCall", "src": "2725:9:1"}, "variableNames": [{"name": "i", "nodeType": "YulIdentifier", "src": "2720:1:1"}]}]}, "pre": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2700:3:1", "statements": []}, "src": "2696:252:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2968:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2979:2:1", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2964:3:1"}, "nodeType": "YulFunctionCall", "src": "2964:18:1"}, {"arguments": [{"name": "tail_2", "nodeType": "YulIdentifier", "src": "2988:6:1"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "2996:9:1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "2984:3:1"}, "nodeType": "YulFunctionCall", "src": "2984:22:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2957:6:1"}, "nodeType": "YulFunctionCall", "src": "2957:50:1"}, "nodeType": "YulExpressionStatement", "src": "2957:50:1"}, {"nodeType": "YulAssignment", "src": "3016:52:1", "value": {"arguments": [{"name": "value2", "nodeType": "YulIdentifier", "src": "3053:6:1"}, {"name": "tail_2", "nodeType": "YulIdentifier", "src": "3061:6:1"}], "functionName": {"name": "abi_encode_array_uint256_dyn", "nodeType": "YulIdentifier", "src": "3024:28:1"}, "nodeType": "YulFunctionCall", "src": "3024:44:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "3016:4:1"}]}, {"expression": {"arguments": [{"name": "value3", "nodeType": "YulIdentifier", "src": "3093:6:1"}, {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "3105:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3116:2:1", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3101:3:1"}, "nodeType": "YulFunctionCall", "src": "3101:18:1"}], "functionName": {"name": "abi_encode_bool", "nodeType": "YulIdentifier", "src": "3077:15:1"}, "nodeType": "YulFunctionCall", "src": "3077:43:1"}, "nodeType": "YulExpressionStatement", "src": "3077:43:1"}, {"expression": {"arguments": [{"name": "value4", "nodeType": "YulIdentifier", "src": "3148:6:1"}, {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "3160:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3171:3:1", "type": "", "value": "128"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3156:3:1"}, "nodeType": "YulFunctionCall", "src": "3156:19:1"}], "functionName": {"name": "abi_encode_address", "nodeType": "YulIdentifier", "src": "3129:18:1"}, "nodeType": "YulFunctionCall", "src": "3129:47:1"}, "nodeType": "YulExpressionStatement", "src": "3129:47:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "3196:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3207:3:1", "type": "", "value": "160"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3192:3:1"}, "nodeType": "YulFunctionCall", "src": "3192:19:1"}, {"name": "value5", "nodeType": "YulIdentifier", "src": "3213:6:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "3185:6:1"}, "nodeType": "YulFunctionCall", "src": "3185:35:1"}, "nodeType": "YulExpressionStatement", "src": "3185:35:1"}]}, "name": "abi_encode_tuple_t_string_memory_ptr_t_array$_t_string_memory_ptr_$dyn_memory_ptr_t_array$_t_uint256_$dyn_memory_ptr_t_bool_t_address_t_uint256__to_t_string_memory_ptr_t_array$_t_string_memory_ptr_$dyn_memory_ptr_t_array$_t_uint256_$dyn_memory_ptr_t_bool_t_address_t_uint256__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "2195:9:1", "type": ""}, {"name": "value5", "nodeType": "YulTypedName", "src": "2206:6:1", "type": ""}, {"name": "value4", "nodeType": "YulTypedName", "src": "2214:6:1", "type": ""}, {"name": "value3", "nodeType": "YulTypedName", "src": "2222:6:1", "type": ""}, {"name": "value2", "nodeType": "YulTypedName", "src": "2230:6:1", "type": ""}, {"name": "value1", "nodeType": "YulTypedName", "src": "2238:6:1", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "2246:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "2257:4:1", "type": ""}], "src": "1891:1335:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3263:95:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3280:1:1", "type": "", "value": "0"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3287:3:1", "type": "", "value": "224"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3292:10:1", "type": "", "value": "0x4e487b71"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "3283:3:1"}, "nodeType": "YulFunctionCall", "src": "3283:20:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "3273:6:1"}, "nodeType": "YulFunctionCall", "src": "3273:31:1"}, "nodeType": "YulExpressionStatement", "src": "3273:31:1"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3320:1:1", "type": "", "value": "4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3323:4:1", "type": "", "value": "0x41"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "3313:6:1"}, "nodeType": "YulFunctionCall", "src": "3313:15:1"}, "nodeType": "YulExpressionStatement", "src": "3313:15:1"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3344:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3347:4:1", "type": "", "value": "0x24"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "3337:6:1"}, "nodeType": "YulFunctionCall", "src": "3337:15:1"}, "nodeType": "YulExpressionStatement", "src": "3337:15:1"}]}, "name": "panic_error_0x41", "nodeType": "YulFunctionDefinition", "src": "3231:127:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3408:230:1", "statements": [{"nodeType": "YulAssignment", "src": "3418:19:1", "value": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3434:2:1", "type": "", "value": "64"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "3428:5:1"}, "nodeType": "YulFunctionCall", "src": "3428:9:1"}, "variableNames": [{"name": "memPtr", "nodeType": "YulIdentifier", "src": "3418:6:1"}]}, {"nodeType": "YulVariableDeclaration", "src": "3446:58:1", "value": {"arguments": [{"name": "memPtr", "nodeType": "YulIdentifier", "src": "3468:6:1"}, {"arguments": [{"arguments": [{"name": "size", "nodeType": "YulIdentifier", "src": "3484:4:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3490:2:1", "type": "", "value": "31"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3480:3:1"}, "nodeType": "YulFunctionCall", "src": "3480:13:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3499:2:1", "type": "", "value": "31"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "3495:3:1"}, "nodeType": "YulFunctionCall", "src": "3495:7:1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "3476:3:1"}, "nodeType": "YulFunctionCall", "src": "3476:27:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3464:3:1"}, "nodeType": "YulFunctionCall", "src": "3464:40:1"}, "variables": [{"name": "newFreePtr", "nodeType": "YulTypedName", "src": "3450:10:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3579:22:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "panic_error_0x41", "nodeType": "YulIdentifier", "src": "3581:16:1"}, "nodeType": "YulFunctionCall", "src": "3581:18:1"}, "nodeType": "YulExpressionStatement", "src": "3581:18:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "newFreePtr", "nodeType": "YulIdentifier", "src": "3522:10:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3534:18:1", "type": "", "value": "0xffffffffffffffff"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "3519:2:1"}, "nodeType": "YulFunctionCall", "src": "3519:34:1"}, {"arguments": [{"name": "newFreePtr", "nodeType": "YulIdentifier", "src": "3558:10:1"}, {"name": "memPtr", "nodeType": "YulIdentifier", "src": "3570:6:1"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "3555:2:1"}, "nodeType": "YulFunctionCall", "src": "3555:22:1"}], "functionName": {"name": "or", "nodeType": "YulIdentifier", "src": "3516:2:1"}, "nodeType": "YulFunctionCall", "src": "3516:62:1"}, "nodeType": "YulIf", "src": "3513:88:1"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3617:2:1", "type": "", "value": "64"}, {"name": "newFreePtr", "nodeType": "YulIdentifier", "src": "3621:10:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "3610:6:1"}, "nodeType": "YulFunctionCall", "src": "3610:22:1"}, "nodeType": "YulExpressionStatement", "src": "3610:22:1"}]}, "name": "allocate_memory", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "size", "nodeType": "YulTypedName", "src": "3388:4:1", "type": ""}], "returnVariables": [{"name": "memPtr", "nodeType": "YulTypedName", "src": "3397:6:1", "type": ""}], "src": "3363:275:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3696:478:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3745:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3754:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3757:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "3747:6:1"}, "nodeType": "YulFunctionCall", "src": "3747:12:1"}, "nodeType": "YulExpressionStatement", "src": "3747:12:1"}]}, "condition": {"arguments": [{"arguments": [{"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "3724:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3732:4:1", "type": "", "value": "0x1f"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3720:3:1"}, "nodeType": "YulFunctionCall", "src": "3720:17:1"}, {"name": "end", "nodeType": "YulIdentifier", "src": "3739:3:1"}], "functionName": {"name": "slt", "nodeType": "YulIdentifier", "src": "3716:3:1"}, "nodeType": "YulFunctionCall", "src": "3716:27:1"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "3709:6:1"}, "nodeType": "YulFunctionCall", "src": "3709:35:1"}, "nodeType": "YulIf", "src": "3706:55:1"}, {"nodeType": "YulVariableDeclaration", "src": "3770:30:1", "value": {"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "3793:6:1"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "3780:12:1"}, "nodeType": "YulFunctionCall", "src": "3780:20:1"}, "variables": [{"name": "_1", "nodeType": "YulTypedName", "src": "3774:2:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3839:22:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "panic_error_0x41", "nodeType": "YulIdentifier", "src": "3841:16:1"}, "nodeType": "YulFunctionCall", "src": "3841:18:1"}, "nodeType": "YulExpressionStatement", "src": "3841:18:1"}]}, "condition": {"arguments": [{"name": "_1", "nodeType": "YulIdentifier", "src": "3815:2:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3819:18:1", "type": "", "value": "0xffffffffffffffff"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "3812:2:1"}, "nodeType": "YulFunctionCall", "src": "3812:26:1"}, "nodeType": "YulIf", "src": "3809:52:1"}, {"nodeType": "YulVariableDeclaration", "src": "3870:70:1", "value": {"arguments": [{"arguments": [{"arguments": [{"arguments": [{"name": "_1", "nodeType": "YulIdentifier", "src": "3913:2:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3917:4:1", "type": "", "value": "0x1f"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3909:3:1"}, "nodeType": "YulFunctionCall", "src": "3909:13:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3928:2:1", "type": "", "value": "31"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "3924:3:1"}, "nodeType": "YulFunctionCall", "src": "3924:7:1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "3905:3:1"}, "nodeType": "YulFunctionCall", "src": "3905:27:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3934:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3901:3:1"}, "nodeType": "YulFunctionCall", "src": "3901:38:1"}], "functionName": {"name": "allocate_memory", "nodeType": "YulIdentifier", "src": "3885:15:1"}, "nodeType": "YulFunctionCall", "src": "3885:55:1"}, "variables": [{"name": "array_1", "nodeType": "YulTypedName", "src": "3874:7:1", "type": ""}]}, {"expression": {"arguments": [{"name": "array_1", "nodeType": "YulIdentifier", "src": "3956:7:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "3965:2:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "3949:6:1"}, "nodeType": "YulFunctionCall", "src": "3949:19:1"}, "nodeType": "YulExpressionStatement", "src": "3949:19:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4016:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4025:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4028:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "4018:6:1"}, "nodeType": "YulFunctionCall", "src": "4018:12:1"}, "nodeType": "YulExpressionStatement", "src": "4018:12:1"}]}, "condition": {"arguments": [{"arguments": [{"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "3991:6:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "3999:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3987:3:1"}, "nodeType": "YulFunctionCall", "src": "3987:15:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4004:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3983:3:1"}, "nodeType": "YulFunctionCall", "src": "3983:26:1"}, {"name": "end", "nodeType": "YulIdentifier", "src": "4011:3:1"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "3980:2:1"}, "nodeType": "YulFunctionCall", "src": "3980:35:1"}, "nodeType": "YulIf", "src": "3977:55:1"}, {"expression": {"arguments": [{"arguments": [{"name": "array_1", "nodeType": "YulIdentifier", "src": "4058:7:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4067:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4054:3:1"}, "nodeType": "YulFunctionCall", "src": "4054:18:1"}, {"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "4078:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4086:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4074:3:1"}, "nodeType": "YulFunctionCall", "src": "4074:17:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "4093:2:1"}], "functionName": {"name": "calldatacopy", "nodeType": "YulIdentifier", "src": "4041:12:1"}, "nodeType": "YulFunctionCall", "src": "4041:55:1"}, "nodeType": "YulExpressionStatement", "src": "4041:55:1"}, {"expression": {"arguments": [{"arguments": [{"arguments": [{"name": "array_1", "nodeType": "YulIdentifier", "src": "4120:7:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "4129:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4116:3:1"}, "nodeType": "YulFunctionCall", "src": "4116:16:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4134:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4112:3:1"}, "nodeType": "YulFunctionCall", "src": "4112:27:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4141:1:1", "type": "", "value": "0"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "4105:6:1"}, "nodeType": "YulFunctionCall", "src": "4105:38:1"}, "nodeType": "YulExpressionStatement", "src": "4105:38:1"}, {"nodeType": "YulAssignment", "src": "4152:16:1", "value": {"name": "array_1", "nodeType": "YulIdentifier", "src": "4161:7:1"}, "variableNames": [{"name": "array", "nodeType": "YulIdentifier", "src": "4152:5:1"}]}]}, "name": "abi_decode_string", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "offset", "nodeType": "YulTypedName", "src": "3670:6:1", "type": ""}, {"name": "end", "nodeType": "YulTypedName", "src": "3678:3:1", "type": ""}], "returnVariables": [{"name": "array", "nodeType": "YulTypedName", "src": "3686:5:1", "type": ""}], "src": "3643:531:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4311:1235:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4357:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4366:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4369:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "4359:6:1"}, "nodeType": "YulFunctionCall", "src": "4359:12:1"}, "nodeType": "YulExpressionStatement", "src": "4359:12:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nodeType": "YulIdentifier", "src": "4332:7:1"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "4341:9:1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "4328:3:1"}, "nodeType": "YulFunctionCall", "src": "4328:23:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4353:2:1", "type": "", "value": "64"}], "functionName": {"name": "slt", "nodeType": "YulIdentifier", "src": "4324:3:1"}, "nodeType": "YulFunctionCall", "src": "4324:32:1"}, "nodeType": "YulIf", "src": "4321:52:1"}, {"nodeType": "YulVariableDeclaration", "src": "4382:37:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "4409:9:1"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "4396:12:1"}, "nodeType": "YulFunctionCall", "src": "4396:23:1"}, "variables": [{"name": "offset", "nodeType": "YulTypedName", "src": "4386:6:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "4428:28:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4438:18:1", "type": "", "value": "0xffffffffffffffff"}, "variables": [{"name": "_1", "nodeType": "YulTypedName", "src": "4432:2:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4483:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4492:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4495:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "4485:6:1"}, "nodeType": "YulFunctionCall", "src": "4485:12:1"}, "nodeType": "YulExpressionStatement", "src": "4485:12:1"}]}, "condition": {"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "4471:6:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "4479:2:1"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "4468:2:1"}, "nodeType": "YulFunctionCall", "src": "4468:14:1"}, "nodeType": "YulIf", "src": "4465:34:1"}, {"nodeType": "YulAssignment", "src": "4508:60:1", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "4540:9:1"}, {"name": "offset", "nodeType": "YulIdentifier", "src": "4551:6:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4536:3:1"}, "nodeType": "YulFunctionCall", "src": "4536:22:1"}, {"name": "dataEnd", "nodeType": "YulIdentifier", "src": "4560:7:1"}], "functionName": {"name": "abi_decode_string", "nodeType": "YulIdentifier", "src": "4518:17:1"}, "nodeType": "YulFunctionCall", "src": "4518:50:1"}, "variableNames": [{"name": "value0", "nodeType": "YulIdentifier", "src": "4508:6:1"}]}, {"nodeType": "YulVariableDeclaration", "src": "4577:12:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4587:2:1", "type": "", "value": "32"}, "variables": [{"name": "_2", "nodeType": "YulTypedName", "src": "4581:2:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "4598:48:1", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "4631:9:1"}, {"name": "_2", "nodeType": "YulIdentifier", "src": "4642:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4627:3:1"}, "nodeType": "YulFunctionCall", "src": "4627:18:1"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "4614:12:1"}, "nodeType": "YulFunctionCall", "src": "4614:32:1"}, "variables": [{"name": "offset_1", "nodeType": "YulTypedName", "src": "4602:8:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4675:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4684:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4687:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "4677:6:1"}, "nodeType": "YulFunctionCall", "src": "4677:12:1"}, "nodeType": "YulExpressionStatement", "src": "4677:12:1"}]}, "condition": {"arguments": [{"name": "offset_1", "nodeType": "YulIdentifier", "src": "4661:8:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "4671:2:1"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "4658:2:1"}, "nodeType": "YulFunctionCall", "src": "4658:16:1"}, "nodeType": "YulIf", "src": "4655:36:1"}, {"nodeType": "YulVariableDeclaration", "src": "4700:34:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "4714:9:1"}, {"name": "offset_1", "nodeType": "YulIdentifier", "src": "4725:8:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4710:3:1"}, "nodeType": "YulFunctionCall", "src": "4710:24:1"}, "variables": [{"name": "_3", "nodeType": "YulTypedName", "src": "4704:2:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4782:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4791:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4794:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "4784:6:1"}, "nodeType": "YulFunctionCall", "src": "4784:12:1"}, "nodeType": "YulExpressionStatement", "src": "4784:12:1"}]}, "condition": {"arguments": [{"arguments": [{"arguments": [{"name": "_3", "nodeType": "YulIdentifier", "src": "4761:2:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4765:4:1", "type": "", "value": "0x1f"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4757:3:1"}, "nodeType": "YulFunctionCall", "src": "4757:13:1"}, {"name": "dataEnd", "nodeType": "YulIdentifier", "src": "4772:7:1"}], "functionName": {"name": "slt", "nodeType": "YulIdentifier", "src": "4753:3:1"}, "nodeType": "YulFunctionCall", "src": "4753:27:1"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "4746:6:1"}, "nodeType": "YulFunctionCall", "src": "4746:35:1"}, "nodeType": "YulIf", "src": "4743:55:1"}, {"nodeType": "YulVariableDeclaration", "src": "4807:26:1", "value": {"arguments": [{"name": "_3", "nodeType": "YulIdentifier", "src": "4830:2:1"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "4817:12:1"}, "nodeType": "YulFunctionCall", "src": "4817:16:1"}, "variables": [{"name": "_4", "nodeType": "YulTypedName", "src": "4811:2:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4856:22:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "panic_error_0x41", "nodeType": "YulIdentifier", "src": "4858:16:1"}, "nodeType": "YulFunctionCall", "src": "4858:18:1"}, "nodeType": "YulExpressionStatement", "src": "4858:18:1"}]}, "condition": {"arguments": [{"name": "_4", "nodeType": "YulIdentifier", "src": "4848:2:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "4852:2:1"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "4845:2:1"}, "nodeType": "YulFunctionCall", "src": "4845:10:1"}, "nodeType": "YulIf", "src": "4842:36:1"}, {"nodeType": "YulVariableDeclaration", "src": "4887:20:1", "value": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4901:1:1", "type": "", "value": "5"}, {"name": "_4", "nodeType": "YulIdentifier", "src": "4904:2:1"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "4897:3:1"}, "nodeType": "YulFunctionCall", "src": "4897:10:1"}, "variables": [{"name": "_5", "nodeType": "YulTypedName", "src": "4891:2:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "4916:39:1", "value": {"arguments": [{"arguments": [{"name": "_5", "nodeType": "YulIdentifier", "src": "4947:2:1"}, {"name": "_2", "nodeType": "YulIdentifier", "src": "4951:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4943:3:1"}, "nodeType": "YulFunctionCall", "src": "4943:11:1"}], "functionName": {"name": "allocate_memory", "nodeType": "YulIdentifier", "src": "4927:15:1"}, "nodeType": "YulFunctionCall", "src": "4927:28:1"}, "variables": [{"name": "dst", "nodeType": "YulTypedName", "src": "4920:3:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "4964:16:1", "value": {"name": "dst", "nodeType": "YulIdentifier", "src": "4977:3:1"}, "variables": [{"name": "dst_1", "nodeType": "YulTypedName", "src": "4968:5:1", "type": ""}]}, {"expression": {"arguments": [{"name": "dst", "nodeType": "YulIdentifier", "src": "4996:3:1"}, {"name": "_4", "nodeType": "YulIdentifier", "src": "5001:2:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "4989:6:1"}, "nodeType": "YulFunctionCall", "src": "4989:15:1"}, "nodeType": "YulExpressionStatement", "src": "4989:15:1"}, {"nodeType": "YulAssignment", "src": "5013:19:1", "value": {"arguments": [{"name": "dst", "nodeType": "YulIdentifier", "src": "5024:3:1"}, {"name": "_2", "nodeType": "YulIdentifier", "src": "5029:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "5020:3:1"}, "nodeType": "YulFunctionCall", "src": "5020:12:1"}, "variableNames": [{"name": "dst", "nodeType": "YulIdentifier", "src": "5013:3:1"}]}, {"nodeType": "YulVariableDeclaration", "src": "5041:34:1", "value": {"arguments": [{"arguments": [{"name": "_3", "nodeType": "YulIdentifier", "src": "5063:2:1"}, {"name": "_5", "nodeType": "YulIdentifier", "src": "5067:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "5059:3:1"}, "nodeType": "YulFunctionCall", "src": "5059:11:1"}, {"name": "_2", "nodeType": "YulIdentifier", "src": "5072:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "5055:3:1"}, "nodeType": "YulFunctionCall", "src": "5055:20:1"}, "variables": [{"name": "srcEnd", "nodeType": "YulTypedName", "src": "5045:6:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5107:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5116:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5119:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "5109:6:1"}, "nodeType": "YulFunctionCall", "src": "5109:12:1"}, "nodeType": "YulExpressionStatement", "src": "5109:12:1"}]}, "condition": {"arguments": [{"name": "srcEnd", "nodeType": "YulIdentifier", "src": "5090:6:1"}, {"name": "dataEnd", "nodeType": "YulIdentifier", "src": "5098:7:1"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "5087:2:1"}, "nodeType": "YulFunctionCall", "src": "5087:19:1"}, "nodeType": "YulIf", "src": "5084:39:1"}, {"nodeType": "YulVariableDeclaration", "src": "5132:22:1", "value": {"arguments": [{"name": "_3", "nodeType": "YulIdentifier", "src": "5147:2:1"}, {"name": "_2", "nodeType": "YulIdentifier", "src": "5151:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "5143:3:1"}, "nodeType": "YulFunctionCall", "src": "5143:11:1"}, "variables": [{"name": "src", "nodeType": "YulTypedName", "src": "5136:3:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5219:297:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "5233:36:1", "value": {"arguments": [{"name": "src", "nodeType": "YulIdentifier", "src": "5265:3:1"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "5252:12:1"}, "nodeType": "YulFunctionCall", "src": "5252:17:1"}, "variables": [{"name": "innerOffset", "nodeType": "YulTypedName", "src": "5237:11:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5317:74:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "5335:11:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5345:1:1", "type": "", "value": "0"}, "variables": [{"name": "_6", "nodeType": "YulTypedName", "src": "5339:2:1", "type": ""}]}, {"expression": {"arguments": [{"name": "_6", "nodeType": "YulIdentifier", "src": "5370:2:1"}, {"name": "_6", "nodeType": "YulIdentifier", "src": "5374:2:1"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "5363:6:1"}, "nodeType": "YulFunctionCall", "src": "5363:14:1"}, "nodeType": "YulExpressionStatement", "src": "5363:14:1"}]}, "condition": {"arguments": [{"name": "innerOffset", "nodeType": "YulIdentifier", "src": "5288:11:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "5301:2:1"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "5285:2:1"}, "nodeType": "YulFunctionCall", "src": "5285:19:1"}, "nodeType": "YulIf", "src": "5282:109:1"}, {"expression": {"arguments": [{"name": "dst", "nodeType": "YulIdentifier", "src": "5411:3:1"}, {"arguments": [{"arguments": [{"arguments": [{"name": "_3", "nodeType": "YulIdentifier", "src": "5442:2:1"}, {"name": "innerOffset", "nodeType": "YulIdentifier", "src": "5446:11:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "5438:3:1"}, "nodeType": "YulFunctionCall", "src": "5438:20:1"}, {"name": "_2", "nodeType": "YulIdentifier", "src": "5460:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "5434:3:1"}, "nodeType": "YulFunctionCall", "src": "5434:29:1"}, {"name": "dataEnd", "nodeType": "YulIdentifier", "src": "5465:7:1"}], "functionName": {"name": "abi_decode_string", "nodeType": "YulIdentifier", "src": "5416:17:1"}, "nodeType": "YulFunctionCall", "src": "5416:57:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "5404:6:1"}, "nodeType": "YulFunctionCall", "src": "5404:70:1"}, "nodeType": "YulExpressionStatement", "src": "5404:70:1"}, {"nodeType": "YulAssignment", "src": "5487:19:1", "value": {"arguments": [{"name": "dst", "nodeType": "YulIdentifier", "src": "5498:3:1"}, {"name": "_2", "nodeType": "YulIdentifier", "src": "5503:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "5494:3:1"}, "nodeType": "YulFunctionCall", "src": "5494:12:1"}, "variableNames": [{"name": "dst", "nodeType": "YulIdentifier", "src": "5487:3:1"}]}]}, "condition": {"arguments": [{"name": "src", "nodeType": "YulIdentifier", "src": "5174:3:1"}, {"name": "srcEnd", "nodeType": "YulIdentifier", "src": "5179:6:1"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "5171:2:1"}, "nodeType": "YulFunctionCall", "src": "5171:15:1"}, "nodeType": "YulForLoop", "post": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5187:23:1", "statements": [{"nodeType": "YulAssignment", "src": "5189:19:1", "value": {"arguments": [{"name": "src", "nodeType": "YulIdentifier", "src": "5200:3:1"}, {"name": "_2", "nodeType": "YulIdentifier", "src": "5205:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "5196:3:1"}, "nodeType": "YulFunctionCall", "src": "5196:12:1"}, "variableNames": [{"name": "src", "nodeType": "YulIdentifier", "src": "5189:3:1"}]}]}, "pre": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5167:3:1", "statements": []}, "src": "5163:353:1"}, {"nodeType": "YulAssignment", "src": "5525:15:1", "value": {"name": "dst_1", "nodeType": "YulIdentifier", "src": "5535:5:1"}, "variableNames": [{"name": "value1", "nodeType": "YulIdentifier", "src": "5525:6:1"}]}]}, "name": "abi_decode_tuple_t_string_memory_ptrt_array$_t_string_memory_ptr_$dyn_memory_ptr", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "4269:9:1", "type": ""}, {"name": "dataEnd", "nodeType": "YulTypedName", "src": "4280:7:1", "type": ""}], "returnVariables": [{"name": "value0", "nodeType": "YulTypedName", "src": "4292:6:1", "type": ""}, {"name": "value1", "nodeType": "YulTypedName", "src": "4300:6:1", "type": ""}], "src": "4179:1367:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5638:167:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5684:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5693:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5696:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "5686:6:1"}, "nodeType": "YulFunctionCall", "src": "5686:12:1"}, "nodeType": "YulExpressionStatement", "src": "5686:12:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nodeType": "YulIdentifier", "src": "5659:7:1"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "5668:9:1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "5655:3:1"}, "nodeType": "YulFunctionCall", "src": "5655:23:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5680:2:1", "type": "", "value": "64"}], "functionName": {"name": "slt", "nodeType": "YulIdentifier", "src": "5651:3:1"}, "nodeType": "YulFunctionCall", "src": "5651:32:1"}, "nodeType": "YulIf", "src": "5648:52:1"}, {"nodeType": "YulAssignment", "src": "5709:39:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "5738:9:1"}], "functionName": {"name": "abi_decode_address", "nodeType": "YulIdentifier", "src": "5719:18:1"}, "nodeType": "YulFunctionCall", "src": "5719:29:1"}, "variableNames": [{"name": "value0", "nodeType": "YulIdentifier", "src": "5709:6:1"}]}, {"nodeType": "YulAssignment", "src": "5757:42:1", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "5784:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5795:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "5780:3:1"}, "nodeType": "YulFunctionCall", "src": "5780:18:1"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "5767:12:1"}, "nodeType": "YulFunctionCall", "src": "5767:32:1"}, "variableNames": [{"name": "value1", "nodeType": "YulIdentifier", "src": "5757:6:1"}]}]}, "name": "abi_decode_tuple_t_addresst_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "5596:9:1", "type": ""}, {"name": "dataEnd", "nodeType": "YulTypedName", "src": "5607:7:1", "type": ""}], "returnVariables": [{"name": "value0", "nodeType": "YulTypedName", "src": "5619:6:1", "type": ""}, {"name": "value1", "nodeType": "YulTypedName", "src": "5627:6:1", "type": ""}], "src": "5551:254:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6009:272:1", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "6026:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6037:3:1", "type": "", "value": "128"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "6019:6:1"}, "nodeType": "YulFunctionCall", "src": "6019:22:1"}, "nodeType": "YulExpressionStatement", "src": "6019:22:1"}, {"nodeType": "YulAssignment", "src": "6050:54:1", "value": {"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "6076:6:1"}, {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "6088:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6099:3:1", "type": "", "value": "128"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "6084:3:1"}, "nodeType": "YulFunctionCall", "src": "6084:19:1"}], "functionName": {"name": "abi_encode_string", "nodeType": "YulIdentifier", "src": "6058:17:1"}, "nodeType": "YulFunctionCall", "src": "6058:46:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "6050:4:1"}]}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "6124:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6135:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "6120:3:1"}, "nodeType": "YulFunctionCall", "src": "6120:18:1"}, {"arguments": [{"arguments": [{"name": "value1", "nodeType": "YulIdentifier", "src": "6154:6:1"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "6147:6:1"}, "nodeType": "YulFunctionCall", "src": "6147:14:1"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "6140:6:1"}, "nodeType": "YulFunctionCall", "src": "6140:22:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "6113:6:1"}, "nodeType": "YulFunctionCall", "src": "6113:50:1"}, "nodeType": "YulExpressionStatement", "src": "6113:50:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "6183:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6194:2:1", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "6179:3:1"}, "nodeType": "YulFunctionCall", "src": "6179:18:1"}, {"arguments": [{"name": "value2", "nodeType": "YulIdentifier", "src": "6203:6:1"}, {"arguments": [{"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6219:3:1", "type": "", "value": "160"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6224:1:1", "type": "", "value": "1"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "6215:3:1"}, "nodeType": "YulFunctionCall", "src": "6215:11:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6228:1:1", "type": "", "value": "1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "6211:3:1"}, "nodeType": "YulFunctionCall", "src": "6211:19:1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "6199:3:1"}, "nodeType": "YulFunctionCall", "src": "6199:32:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "6172:6:1"}, "nodeType": "YulFunctionCall", "src": "6172:60:1"}, "nodeType": "YulExpressionStatement", "src": "6172:60:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "6252:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6263:2:1", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "6248:3:1"}, "nodeType": "YulFunctionCall", "src": "6248:18:1"}, {"name": "value3", "nodeType": "YulIdentifier", "src": "6268:6:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "6241:6:1"}, "nodeType": "YulFunctionCall", "src": "6241:34:1"}, "nodeType": "YulExpressionStatement", "src": "6241:34:1"}]}, "name": "abi_encode_tuple_t_string_memory_ptr_t_bool_t_address_t_uint256__to_t_string_memory_ptr_t_bool_t_address_t_uint256__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "5954:9:1", "type": ""}, {"name": "value3", "nodeType": "YulTypedName", "src": "5965:6:1", "type": ""}, {"name": "value2", "nodeType": "YulTypedName", "src": "5973:6:1", "type": ""}, {"name": "value1", "nodeType": "YulTypedName", "src": "5981:6:1", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "5989:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "6000:4:1", "type": ""}], "src": "5810:471:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6373:161:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6419:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6428:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6431:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "6421:6:1"}, "nodeType": "YulFunctionCall", "src": "6421:12:1"}, "nodeType": "YulExpressionStatement", "src": "6421:12:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nodeType": "YulIdentifier", "src": "6394:7:1"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "6403:9:1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "6390:3:1"}, "nodeType": "YulFunctionCall", "src": "6390:23:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6415:2:1", "type": "", "value": "64"}], "functionName": {"name": "slt", "nodeType": "YulIdentifier", "src": "6386:3:1"}, "nodeType": "YulFunctionCall", "src": "6386:32:1"}, "nodeType": "YulIf", "src": "6383:52:1"}, {"nodeType": "YulAssignment", "src": "6444:33:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "6467:9:1"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "6454:12:1"}, "nodeType": "YulFunctionCall", "src": "6454:23:1"}, "variableNames": [{"name": "value0", "nodeType": "YulIdentifier", "src": "6444:6:1"}]}, {"nodeType": "YulAssignment", "src": "6486:42:1", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "6513:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6524:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "6509:3:1"}, "nodeType": "YulFunctionCall", "src": "6509:18:1"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "6496:12:1"}, "nodeType": "YulFunctionCall", "src": "6496:32:1"}, "variableNames": [{"name": "value1", "nodeType": "YulIdentifier", "src": "6486:6:1"}]}]}, "name": "abi_decode_tuple_t_uint256t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "6331:9:1", "type": ""}, {"name": "dataEnd", "nodeType": "YulTypedName", "src": "6342:7:1", "type": ""}], "returnVariables": [{"name": "value0", "nodeType": "YulTypedName", "src": "6354:6:1", "type": ""}, {"name": "value1", "nodeType": "YulTypedName", "src": "6362:6:1", "type": ""}], "src": "6286:248:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6634:92:1", "statements": [{"nodeType": "YulAssignment", "src": "6644:26:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "6656:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6667:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "6652:3:1"}, "nodeType": "YulFunctionCall", "src": "6652:18:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "6644:4:1"}]}, {"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "6686:9:1"}, {"arguments": [{"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "6711:6:1"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "6704:6:1"}, "nodeType": "YulFunctionCall", "src": "6704:14:1"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "6697:6:1"}, "nodeType": "YulFunctionCall", "src": "6697:22:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "6679:6:1"}, "nodeType": "YulFunctionCall", "src": "6679:41:1"}, "nodeType": "YulExpressionStatement", "src": "6679:41:1"}]}, "name": "abi_encode_tuple_t_bool__to_t_bool__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "6603:9:1", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "6614:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "6625:4:1", "type": ""}], "src": "6539:187:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6801:116:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6847:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6856:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6859:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "6849:6:1"}, "nodeType": "YulFunctionCall", "src": "6849:12:1"}, "nodeType": "YulExpressionStatement", "src": "6849:12:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nodeType": "YulIdentifier", "src": "6822:7:1"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "6831:9:1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "6818:3:1"}, "nodeType": "YulFunctionCall", "src": "6818:23:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6843:2:1", "type": "", "value": "32"}], "functionName": {"name": "slt", "nodeType": "YulIdentifier", "src": "6814:3:1"}, "nodeType": "YulFunctionCall", "src": "6814:32:1"}, "nodeType": "YulIf", "src": "6811:52:1"}, {"nodeType": "YulAssignment", "src": "6872:39:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "6901:9:1"}], "functionName": {"name": "abi_decode_address", "nodeType": "YulIdentifier", "src": "6882:18:1"}, "nodeType": "YulFunctionCall", "src": "6882:29:1"}, "variableNames": [{"name": "value0", "nodeType": "YulIdentifier", "src": "6872:6:1"}]}]}, "name": "abi_decode_tuple_t_address", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "6767:9:1", "type": ""}, {"name": "dataEnd", "nodeType": "YulTypedName", "src": "6778:7:1", "type": ""}], "returnVariables": [{"name": "value0", "nodeType": "YulTypedName", "src": "6790:6:1", "type": ""}], "src": "6731:186:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7073:110:1", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "7090:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7101:2:1", "type": "", "value": "32"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "7083:6:1"}, "nodeType": "YulFunctionCall", "src": "7083:21:1"}, "nodeType": "YulExpressionStatement", "src": "7083:21:1"}, {"nodeType": "YulAssignment", "src": "7113:64:1", "value": {"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "7150:6:1"}, {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "7162:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7173:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "7158:3:1"}, "nodeType": "YulFunctionCall", "src": "7158:18:1"}], "functionName": {"name": "abi_encode_array_uint256_dyn", "nodeType": "YulIdentifier", "src": "7121:28:1"}, "nodeType": "YulFunctionCall", "src": "7121:56:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "7113:4:1"}]}]}, "name": "abi_encode_tuple_t_array$_t_uint256_$dyn_memory_ptr__to_t_array$_t_uint256_$dyn_memory_ptr__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "7042:9:1", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "7053:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "7064:4:1", "type": ""}], "src": "6922:261:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7362:169:1", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "7379:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7390:2:1", "type": "", "value": "32"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "7372:6:1"}, "nodeType": "YulFunctionCall", "src": "7372:21:1"}, "nodeType": "YulExpressionStatement", "src": "7372:21:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "7413:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7424:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "7409:3:1"}, "nodeType": "YulFunctionCall", "src": "7409:18:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7429:2:1", "type": "", "value": "19"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "7402:6:1"}, "nodeType": "YulFunctionCall", "src": "7402:30:1"}, "nodeType": "YulExpressionStatement", "src": "7402:30:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "7452:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7463:2:1", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "7448:3:1"}, "nodeType": "YulFunctionCall", "src": "7448:18:1"}, {"hexValue": "506f6c6c20646f6573206e6f74206578697374", "kind": "string", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7468:21:1", "type": "", "value": "Poll does not exist"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "7441:6:1"}, "nodeType": "YulFunctionCall", "src": "7441:49:1"}, "nodeType": "YulExpressionStatement", "src": "7441:49:1"}, {"nodeType": "YulAssignment", "src": "7499:26:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "7511:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7522:2:1", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "7507:3:1"}, "nodeType": "YulFunctionCall", "src": "7507:18:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "7499:4:1"}]}]}, "name": "abi_encode_tuple_t_stringliteral_a570800e348d215f0f8e7aaa1afa84c103bca1d27fa74d7cf9c25b83f991280b__to_t_string_memory_ptr__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "7339:9:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "7353:4:1", "type": ""}], "src": "7188:343:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7568:95:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7585:1:1", "type": "", "value": "0"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7592:3:1", "type": "", "value": "224"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7597:10:1", "type": "", "value": "0x4e487b71"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "7588:3:1"}, "nodeType": "YulFunctionCall", "src": "7588:20:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "7578:6:1"}, "nodeType": "YulFunctionCall", "src": "7578:31:1"}, "nodeType": "YulExpressionStatement", "src": "7578:31:1"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7625:1:1", "type": "", "value": "4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7628:4:1", "type": "", "value": "0x32"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "7618:6:1"}, "nodeType": "YulFunctionCall", "src": "7618:15:1"}, "nodeType": "YulExpressionStatement", "src": "7618:15:1"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7649:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7652:4:1", "type": "", "value": "0x24"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "7642:6:1"}, "nodeType": "YulFunctionCall", "src": "7642:15:1"}, "nodeType": "YulExpressionStatement", "src": "7642:15:1"}]}, "name": "panic_error_0x32", "nodeType": "YulFunctionDefinition", "src": "7536:127:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7842:168:1", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "7859:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7870:2:1", "type": "", "value": "32"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "7852:6:1"}, "nodeType": "YulFunctionCall", "src": "7852:21:1"}, "nodeType": "YulExpressionStatement", "src": "7852:21:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "7893:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7904:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "7889:3:1"}, "nodeType": "YulFunctionCall", "src": "7889:18:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7909:2:1", "type": "", "value": "18"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "7882:6:1"}, "nodeType": "YulFunctionCall", "src": "7882:30:1"}, "nodeType": "YulExpressionStatement", "src": "7882:30:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "7932:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7943:2:1", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "7928:3:1"}, "nodeType": "YulFunctionCall", "src": "7928:18:1"}, {"hexValue": "5573657220686173206e6f7420766f746564", "kind": "string", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7948:20:1", "type": "", "value": "User has not voted"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "7921:6:1"}, "nodeType": "YulFunctionCall", "src": "7921:48:1"}, "nodeType": "YulExpressionStatement", "src": "7921:48:1"}, {"nodeType": "YulAssignment", "src": "7978:26:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "7990:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8001:2:1", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "7986:3:1"}, "nodeType": "YulFunctionCall", "src": "7986:18:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "7978:4:1"}]}]}, "name": "abi_encode_tuple_t_stringliteral_ec1b155bf1042601d514070206494c49df2fdba4a1da92fad635a0f6e770613f__to_t_string_memory_ptr__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "7819:9:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "7833:4:1", "type": ""}], "src": "7668:342:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8070:325:1", "statements": [{"nodeType": "YulAssignment", "src": "8080:22:1", "value": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8094:1:1", "type": "", "value": "1"}, {"name": "data", "nodeType": "YulIdentifier", "src": "8097:4:1"}], "functionName": {"name": "shr", "nodeType": "YulIdentifier", "src": "8090:3:1"}, "nodeType": "YulFunctionCall", "src": "8090:12:1"}, "variableNames": [{"name": "length", "nodeType": "YulIdentifier", "src": "8080:6:1"}]}, {"nodeType": "YulVariableDeclaration", "src": "8111:38:1", "value": {"arguments": [{"name": "data", "nodeType": "YulIdentifier", "src": "8141:4:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8147:1:1", "type": "", "value": "1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "8137:3:1"}, "nodeType": "YulFunctionCall", "src": "8137:12:1"}, "variables": [{"name": "outOfPlaceEncoding", "nodeType": "YulTypedName", "src": "8115:18:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8188:31:1", "statements": [{"nodeType": "YulAssignment", "src": "8190:27:1", "value": {"arguments": [{"name": "length", "nodeType": "YulIdentifier", "src": "8204:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8212:4:1", "type": "", "value": "0x7f"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "8200:3:1"}, "nodeType": "YulFunctionCall", "src": "8200:17:1"}, "variableNames": [{"name": "length", "nodeType": "YulIdentifier", "src": "8190:6:1"}]}]}, "condition": {"arguments": [{"name": "outOfPlaceEncoding", "nodeType": "YulIdentifier", "src": "8168:18:1"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "8161:6:1"}, "nodeType": "YulFunctionCall", "src": "8161:26:1"}, "nodeType": "YulIf", "src": "8158:61:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8278:111:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8299:1:1", "type": "", "value": "0"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8306:3:1", "type": "", "value": "224"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8311:10:1", "type": "", "value": "0x4e487b71"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "8302:3:1"}, "nodeType": "YulFunctionCall", "src": "8302:20:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "8292:6:1"}, "nodeType": "YulFunctionCall", "src": "8292:31:1"}, "nodeType": "YulExpressionStatement", "src": "8292:31:1"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8343:1:1", "type": "", "value": "4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8346:4:1", "type": "", "value": "0x22"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "8336:6:1"}, "nodeType": "YulFunctionCall", "src": "8336:15:1"}, "nodeType": "YulExpressionStatement", "src": "8336:15:1"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8371:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8374:4:1", "type": "", "value": "0x24"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "8364:6:1"}, "nodeType": "YulFunctionCall", "src": "8364:15:1"}, "nodeType": "YulExpressionStatement", "src": "8364:15:1"}]}, "condition": {"arguments": [{"name": "outOfPlaceEncoding", "nodeType": "YulIdentifier", "src": "8234:18:1"}, {"arguments": [{"name": "length", "nodeType": "YulIdentifier", "src": "8257:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8265:2:1", "type": "", "value": "32"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "8254:2:1"}, "nodeType": "YulFunctionCall", "src": "8254:14:1"}], "functionName": {"name": "eq", "nodeType": "YulIdentifier", "src": "8231:2:1"}, "nodeType": "YulFunctionCall", "src": "8231:38:1"}, "nodeType": "YulIf", "src": "8228:161:1"}]}, "name": "extract_byte_array_length", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "data", "nodeType": "YulTypedName", "src": "8050:4:1", "type": ""}], "returnVariables": [{"name": "length", "nodeType": "YulTypedName", "src": "8059:6:1", "type": ""}], "src": "8015:380:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8574:180:1", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "8591:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8602:2:1", "type": "", "value": "32"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "8584:6:1"}, "nodeType": "YulFunctionCall", "src": "8584:21:1"}, "nodeType": "YulExpressionStatement", "src": "8584:21:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "8625:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8636:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "8621:3:1"}, "nodeType": "YulFunctionCall", "src": "8621:18:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8641:2:1", "type": "", "value": "30"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "8614:6:1"}, "nodeType": "YulFunctionCall", "src": "8614:30:1"}, "nodeType": "YulExpressionStatement", "src": "8614:30:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "8664:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8675:2:1", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "8660:3:1"}, "nodeType": "YulFunctionCall", "src": "8660:18:1"}, {"hexValue": "4f6e6c792063726561746f722063616e20746f67676c6520737461747573", "kind": "string", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8680:32:1", "type": "", "value": "Only creator can toggle status"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "8653:6:1"}, "nodeType": "YulFunctionCall", "src": "8653:60:1"}, "nodeType": "YulExpressionStatement", "src": "8653:60:1"}, {"nodeType": "YulAssignment", "src": "8722:26:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "8734:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8745:2:1", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "8730:3:1"}, "nodeType": "YulFunctionCall", "src": "8730:18:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "8722:4:1"}]}]}, "name": "abi_encode_tuple_t_stringliteral_39d63f22c983e0a835c80b26c00e97ac46e50731b96804551d851bf715b5fd2c__to_t_string_memory_ptr__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "8551:9:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "8565:4:1", "type": ""}], "src": "8400:354:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8933:174:1", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "8950:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8961:2:1", "type": "", "value": "32"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "8943:6:1"}, "nodeType": "YulFunctionCall", "src": "8943:21:1"}, "nodeType": "YulExpressionStatement", "src": "8943:21:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "8984:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8995:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "8980:3:1"}, "nodeType": "YulFunctionCall", "src": "8980:18:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9000:2:1", "type": "", "value": "24"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "8973:6:1"}, "nodeType": "YulFunctionCall", "src": "8973:30:1"}, "nodeType": "YulExpressionStatement", "src": "8973:30:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "9023:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9034:2:1", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "9019:3:1"}, "nodeType": "YulFunctionCall", "src": "9019:18:1"}, {"hexValue": "5175657374696f6e2063616e6e6f7420626520656d707479", "kind": "string", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9039:26:1", "type": "", "value": "Question cannot be empty"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "9012:6:1"}, "nodeType": "YulFunctionCall", "src": "9012:54:1"}, "nodeType": "YulExpressionStatement", "src": "9012:54:1"}, {"nodeType": "YulAssignment", "src": "9075:26:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "9087:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9098:2:1", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "9083:3:1"}, "nodeType": "YulFunctionCall", "src": "9083:18:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "9075:4:1"}]}]}, "name": "abi_encode_tuple_t_stringliteral_e91f32e8485e8cc61ee75db81581675d5614973e773b4b2d6682176c252ce441__to_t_string_memory_ptr__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "8910:9:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "8924:4:1", "type": ""}], "src": "8759:348:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9286:177:1", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "9303:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9314:2:1", "type": "", "value": "32"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "9296:6:1"}, "nodeType": "YulFunctionCall", "src": "9296:21:1"}, "nodeType": "YulExpressionStatement", "src": "9296:21:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "9337:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9348:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "9333:3:1"}, "nodeType": "YulFunctionCall", "src": "9333:18:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9353:2:1", "type": "", "value": "27"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "9326:6:1"}, "nodeType": "YulFunctionCall", "src": "9326:30:1"}, "nodeType": "YulExpressionStatement", "src": "9326:30:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "9376:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9387:2:1", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "9372:3:1"}, "nodeType": "YulFunctionCall", "src": "9372:18:1"}, {"hexValue": "4174206c656173742032206f7074696f6e73207265717569726564", "kind": "string", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9392:29:1", "type": "", "value": "At least 2 options required"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "9365:6:1"}, "nodeType": "YulFunctionCall", "src": "9365:57:1"}, "nodeType": "YulExpressionStatement", "src": "9365:57:1"}, {"nodeType": "YulAssignment", "src": "9431:26:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "9443:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9454:2:1", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "9439:3:1"}, "nodeType": "YulFunctionCall", "src": "9439:18:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "9431:4:1"}]}]}, "name": "abi_encode_tuple_t_stringliteral_8df94984f0986c4b0f652788cbe872845edfb9f154cbd9bb376c68bda91b6cbd__to_t_string_memory_ptr__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "9263:9:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "9277:4:1", "type": ""}], "src": "9112:351:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9642:166:1", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "9659:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9670:2:1", "type": "", "value": "32"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "9652:6:1"}, "nodeType": "YulFunctionCall", "src": "9652:21:1"}, "nodeType": "YulExpressionStatement", "src": "9652:21:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "9693:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9704:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "9689:3:1"}, "nodeType": "YulFunctionCall", "src": "9689:18:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9709:2:1", "type": "", "value": "16"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "9682:6:1"}, "nodeType": "YulFunctionCall", "src": "9682:30:1"}, "nodeType": "YulExpressionStatement", "src": "9682:30:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "9732:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9743:2:1", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "9728:3:1"}, "nodeType": "YulFunctionCall", "src": "9728:18:1"}, {"hexValue": "546f6f206d616e79206f7074696f6e73", "kind": "string", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9748:18:1", "type": "", "value": "Too many options"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "9721:6:1"}, "nodeType": "YulFunctionCall", "src": "9721:46:1"}, "nodeType": "YulExpressionStatement", "src": "9721:46:1"}, {"nodeType": "YulAssignment", "src": "9776:26:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "9788:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9799:2:1", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "9784:3:1"}, "nodeType": "YulFunctionCall", "src": "9784:18:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "9776:4:1"}]}]}, "name": "abi_encode_tuple_t_stringliteral_b5c2a862288076e78ffb9f7b2c2d4242ebde1778466579712ffc2d1471c123ec__to_t_string_memory_ptr__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "9619:9:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "9633:4:1", "type": ""}], "src": "9468:340:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9987:172:1", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "10004:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10015:2:1", "type": "", "value": "32"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "9997:6:1"}, "nodeType": "YulFunctionCall", "src": "9997:21:1"}, "nodeType": "YulExpressionStatement", "src": "9997:21:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "10038:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10049:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "10034:3:1"}, "nodeType": "YulFunctionCall", "src": "10034:18:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10054:2:1", "type": "", "value": "22"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "10027:6:1"}, "nodeType": "YulFunctionCall", "src": "10027:30:1"}, "nodeType": "YulExpressionStatement", "src": "10027:30:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "10077:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10088:2:1", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "10073:3:1"}, "nodeType": "YulFunctionCall", "src": "10073:18:1"}, {"hexValue": "4f7074696f6e2063616e6e6f7420626520656d707479", "kind": "string", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10093:24:1", "type": "", "value": "Option cannot be empty"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "10066:6:1"}, "nodeType": "YulFunctionCall", "src": "10066:52:1"}, "nodeType": "YulExpressionStatement", "src": "10066:52:1"}, {"nodeType": "YulAssignment", "src": "10127:26:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "10139:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10150:2:1", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "10135:3:1"}, "nodeType": "YulFunctionCall", "src": "10135:18:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "10127:4:1"}]}]}, "name": "abi_encode_tuple_t_stringliteral_e951eb8529546f8abd8d997e0753f069f65b139bcb89559e3d3f221bef3b55a5__to_t_string_memory_ptr__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "9964:9:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "9978:4:1", "type": ""}], "src": "9813:346:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10211:185:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10250:111:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10271:1:1", "type": "", "value": "0"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10278:3:1", "type": "", "value": "224"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10283:10:1", "type": "", "value": "0x4e487b71"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "10274:3:1"}, "nodeType": "YulFunctionCall", "src": "10274:20:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "10264:6:1"}, "nodeType": "YulFunctionCall", "src": "10264:31:1"}, "nodeType": "YulExpressionStatement", "src": "10264:31:1"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10315:1:1", "type": "", "value": "4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10318:4:1", "type": "", "value": "0x11"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "10308:6:1"}, "nodeType": "YulFunctionCall", "src": "10308:15:1"}, "nodeType": "YulExpressionStatement", "src": "10308:15:1"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10343:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10346:4:1", "type": "", "value": "0x24"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "10336:6:1"}, "nodeType": "YulFunctionCall", "src": "10336:15:1"}, "nodeType": "YulExpressionStatement", "src": "10336:15:1"}]}, "condition": {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "10227:5:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10238:1:1", "type": "", "value": "0"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "10234:3:1"}, "nodeType": "YulFunctionCall", "src": "10234:6:1"}], "functionName": {"name": "eq", "nodeType": "YulIdentifier", "src": "10224:2:1"}, "nodeType": "YulFunctionCall", "src": "10224:17:1"}, "nodeType": "YulIf", "src": "10221:140:1"}, {"nodeType": "YulAssignment", "src": "10370:20:1", "value": {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "10381:5:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10388:1:1", "type": "", "value": "1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "10377:3:1"}, "nodeType": "YulFunctionCall", "src": "10377:13:1"}, "variableNames": [{"name": "ret", "nodeType": "YulIdentifier", "src": "10370:3:1"}]}]}, "name": "increment_t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "10193:5:1", "type": ""}], "returnVariables": [{"name": "ret", "nodeType": "YulTypedName", "src": "10203:3:1", "type": ""}], "src": "10164:232:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10457:65:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10474:1:1", "type": "", "value": "0"}, {"name": "ptr", "nodeType": "YulIdentifier", "src": "10477:3:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "10467:6:1"}, "nodeType": "YulFunctionCall", "src": "10467:14:1"}, "nodeType": "YulExpressionStatement", "src": "10467:14:1"}, {"nodeType": "YulAssignment", "src": "10490:26:1", "value": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10508:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10511:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "keccak256", "nodeType": "YulIdentifier", "src": "10498:9:1"}, "nodeType": "YulFunctionCall", "src": "10498:18:1"}, "variableNames": [{"name": "data", "nodeType": "YulIdentifier", "src": "10490:4:1"}]}]}, "name": "array_dataslot_string_storage", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "ptr", "nodeType": "YulTypedName", "src": "10440:3:1", "type": ""}], "returnVariables": [{"name": "data", "nodeType": "YulTypedName", "src": "10448:4:1", "type": ""}], "src": "10401:121:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10608:464:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10641:425:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "10655:11:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10665:1:1", "type": "", "value": "0"}, "variables": [{"name": "_1", "nodeType": "YulTypedName", "src": "10659:2:1", "type": ""}]}, {"expression": {"arguments": [{"name": "_1", "nodeType": "YulIdentifier", "src": "10686:2:1"}, {"name": "array", "nodeType": "YulIdentifier", "src": "10690:5:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "10679:6:1"}, "nodeType": "YulFunctionCall", "src": "10679:17:1"}, "nodeType": "YulExpressionStatement", "src": "10679:17:1"}, {"nodeType": "YulVariableDeclaration", "src": "10709:31:1", "value": {"arguments": [{"name": "_1", "nodeType": "YulIdentifier", "src": "10731:2:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10735:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "keccak256", "nodeType": "YulIdentifier", "src": "10721:9:1"}, "nodeType": "YulFunctionCall", "src": "10721:19:1"}, "variables": [{"name": "data", "nodeType": "YulTypedName", "src": "10713:4:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "10753:57:1", "value": {"arguments": [{"name": "data", "nodeType": "YulIdentifier", "src": "10776:4:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10786:1:1", "type": "", "value": "5"}, {"arguments": [{"name": "startIndex", "nodeType": "YulIdentifier", "src": "10793:10:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10805:2:1", "type": "", "value": "31"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "10789:3:1"}, "nodeType": "YulFunctionCall", "src": "10789:19:1"}], "functionName": {"name": "shr", "nodeType": "YulIdentifier", "src": "10782:3:1"}, "nodeType": "YulFunctionCall", "src": "10782:27:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "10772:3:1"}, "nodeType": "YulFunctionCall", "src": "10772:38:1"}, "variables": [{"name": "deleteStart", "nodeType": "YulTypedName", "src": "10757:11:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10847:23:1", "statements": [{"nodeType": "YulAssignment", "src": "10849:19:1", "value": {"name": "data", "nodeType": "YulIdentifier", "src": "10864:4:1"}, "variableNames": [{"name": "deleteStart", "nodeType": "YulIdentifier", "src": "10849:11:1"}]}]}, "condition": {"arguments": [{"name": "startIndex", "nodeType": "YulIdentifier", "src": "10829:10:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10841:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "10826:2:1"}, "nodeType": "YulFunctionCall", "src": "10826:20:1"}, "nodeType": "YulIf", "src": "10823:47:1"}, {"nodeType": "YulVariableDeclaration", "src": "10883:41:1", "value": {"arguments": [{"name": "data", "nodeType": "YulIdentifier", "src": "10897:4:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10907:1:1", "type": "", "value": "5"}, {"arguments": [{"name": "len", "nodeType": "YulIdentifier", "src": "10914:3:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10919:2:1", "type": "", "value": "31"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "10910:3:1"}, "nodeType": "YulFunctionCall", "src": "10910:12:1"}], "functionName": {"name": "shr", "nodeType": "YulIdentifier", "src": "10903:3:1"}, "nodeType": "YulFunctionCall", "src": "10903:20:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "10893:3:1"}, "nodeType": "YulFunctionCall", "src": "10893:31:1"}, "variables": [{"name": "_2", "nodeType": "YulTypedName", "src": "10887:2:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "10937:24:1", "value": {"name": "deleteStart", "nodeType": "YulIdentifier", "src": "10950:11:1"}, "variables": [{"name": "start", "nodeType": "YulTypedName", "src": "10941:5:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "11035:21:1", "statements": [{"expression": {"arguments": [{"name": "start", "nodeType": "YulIdentifier", "src": "11044:5:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "11051:2:1"}], "functionName": {"name": "sstore", "nodeType": "YulIdentifier", "src": "11037:6:1"}, "nodeType": "YulFunctionCall", "src": "11037:17:1"}, "nodeType": "YulExpressionStatement", "src": "11037:17:1"}]}, "condition": {"arguments": [{"name": "start", "nodeType": "YulIdentifier", "src": "10985:5:1"}, {"name": "_2", "nodeType": "YulIdentifier", "src": "10992:2:1"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "10982:2:1"}, "nodeType": "YulFunctionCall", "src": "10982:13:1"}, "nodeType": "YulForLoop", "post": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10996:26:1", "statements": [{"nodeType": "YulAssignment", "src": "10998:22:1", "value": {"arguments": [{"name": "start", "nodeType": "YulIdentifier", "src": "11011:5:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "11018:1:1", "type": "", "value": "1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "11007:3:1"}, "nodeType": "YulFunctionCall", "src": "11007:13:1"}, "variableNames": [{"name": "start", "nodeType": "YulIdentifier", "src": "10998:5:1"}]}]}, "pre": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10978:3:1", "statements": []}, "src": "10974:82:1"}]}, "condition": {"arguments": [{"name": "len", "nodeType": "YulIdentifier", "src": "10624:3:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10629:2:1", "type": "", "value": "31"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "10621:2:1"}, "nodeType": "YulFunctionCall", "src": "10621:11:1"}, "nodeType": "YulIf", "src": "10618:448:1"}]}, "name": "clean_up_bytearray_end_slots_string_storage", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "array", "nodeType": "YulTypedName", "src": "10580:5:1", "type": ""}, {"name": "len", "nodeType": "YulTypedName", "src": "10587:3:1", "type": ""}, {"name": "startIndex", "nodeType": "YulTypedName", "src": "10592:10:1", "type": ""}], "src": "10527:545:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "11162:81:1", "statements": [{"nodeType": "YulAssignment", "src": "11172:65:1", "value": {"arguments": [{"arguments": [{"name": "data", "nodeType": "YulIdentifier", "src": "11187:4:1"}, {"arguments": [{"arguments": [{"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "11205:1:1", "type": "", "value": "3"}, {"name": "len", "nodeType": "YulIdentifier", "src": "11208:3:1"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "11201:3:1"}, "nodeType": "YulFunctionCall", "src": "11201:11:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "11218:1:1", "type": "", "value": "0"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "11214:3:1"}, "nodeType": "YulFunctionCall", "src": "11214:6:1"}], "functionName": {"name": "shr", "nodeType": "YulIdentifier", "src": "11197:3:1"}, "nodeType": "YulFunctionCall", "src": "11197:24:1"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "11193:3:1"}, "nodeType": "YulFunctionCall", "src": "11193:29:1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "11183:3:1"}, "nodeType": "YulFunctionCall", "src": "11183:40:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "11229:1:1", "type": "", "value": "1"}, {"name": "len", "nodeType": "YulIdentifier", "src": "11232:3:1"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "11225:3:1"}, "nodeType": "YulFunctionCall", "src": "11225:11:1"}], "functionName": {"name": "or", "nodeType": "YulIdentifier", "src": "11180:2:1"}, "nodeType": "YulFunctionCall", "src": "11180:57:1"}, "variableNames": [{"name": "used", "nodeType": "YulIdentifier", "src": "11172:4:1"}]}]}, "name": "extract_used_part_and_set_length_of_short_byte_array", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "data", "nodeType": "YulTypedName", "src": "11139:4:1", "type": ""}, {"name": "len", "nodeType": "YulTypedName", "src": "11145:3:1", "type": ""}], "returnVariables": [{"name": "used", "nodeType": "YulTypedName", "src": "11153:4:1", "type": ""}], "src": "11077:166:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "11344:1256:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "11354:24:1", "value": {"arguments": [{"name": "src", "nodeType": "YulIdentifier", "src": "11374:3:1"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "11368:5:1"}, "nodeType": "YulFunctionCall", "src": "11368:10:1"}, "variables": [{"name": "newLen", "nodeType": "YulTypedName", "src": "11358:6:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "11421:22:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "panic_error_0x41", "nodeType": "YulIdentifier", "src": "11423:16:1"}, "nodeType": "YulFunctionCall", "src": "11423:18:1"}, "nodeType": "YulExpressionStatement", "src": "11423:18:1"}]}, "condition": {"arguments": [{"name": "newLen", "nodeType": "YulIdentifier", "src": "11393:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "11401:18:1", "type": "", "value": "0xffffffffffffffff"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "11390:2:1"}, "nodeType": "YulFunctionCall", "src": "11390:30:1"}, "nodeType": "YulIf", "src": "11387:56:1"}, {"expression": {"arguments": [{"name": "slot", "nodeType": "YulIdentifier", "src": "11496:4:1"}, {"arguments": [{"arguments": [{"name": "slot", "nodeType": "YulIdentifier", "src": "11534:4:1"}], "functionName": {"name": "sload", "nodeType": "YulIdentifier", "src": "11528:5:1"}, "nodeType": "YulFunctionCall", "src": "11528:11:1"}], "functionName": {"name": "extract_byte_array_length", "nodeType": "YulIdentifier", "src": "11502:25:1"}, "nodeType": "YulFunctionCall", "src": "11502:38:1"}, {"name": "newLen", "nodeType": "YulIdentifier", "src": "11542:6:1"}], "functionName": {"name": "clean_up_bytearray_end_slots_string_storage", "nodeType": "YulIdentifier", "src": "11452:43:1"}, "nodeType": "YulFunctionCall", "src": "11452:97:1"}, "nodeType": "YulExpressionStatement", "src": "11452:97:1"}, {"nodeType": "YulVariableDeclaration", "src": "11558:18:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "11575:1:1", "type": "", "value": "0"}, "variables": [{"name": "srcOffset", "nodeType": "YulTypedName", "src": "11562:9:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "11585:23:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "11604:4:1", "type": "", "value": "0x20"}, "variables": [{"name": "srcOffset_1", "nodeType": "YulTypedName", "src": "11589:11:1", "type": ""}]}, {"nodeType": "YulAssignment", "src": "11617:24:1", "value": {"name": "srcOffset_1", "nodeType": "YulIdentifier", "src": "11630:11:1"}, "variableNames": [{"name": "srcOffset", "nodeType": "YulIdentifier", "src": "11617:9:1"}]}, {"cases": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "11687:656:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "11701:35:1", "value": {"arguments": [{"name": "newLen", "nodeType": "YulIdentifier", "src": "11720:6:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "11732:2:1", "type": "", "value": "31"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "11728:3:1"}, "nodeType": "YulFunctionCall", "src": "11728:7:1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "11716:3:1"}, "nodeType": "YulFunctionCall", "src": "11716:20:1"}, "variables": [{"name": "loopEnd", "nodeType": "YulTypedName", "src": "11705:7:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "11749:49:1", "value": {"arguments": [{"name": "slot", "nodeType": "YulIdentifier", "src": "11793:4:1"}], "functionName": {"name": "array_dataslot_string_storage", "nodeType": "YulIdentifier", "src": "11763:29:1"}, "nodeType": "YulFunctionCall", "src": "11763:35:1"}, "variables": [{"name": "dstPtr", "nodeType": "YulTypedName", "src": "11753:6:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "11811:10:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "11820:1:1", "type": "", "value": "0"}, "variables": [{"name": "i", "nodeType": "YulTypedName", "src": "11815:1:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "11898:172:1", "statements": [{"expression": {"arguments": [{"name": "dstPtr", "nodeType": "YulIdentifier", "src": "11923:6:1"}, {"arguments": [{"arguments": [{"name": "src", "nodeType": "YulIdentifier", "src": "11941:3:1"}, {"name": "srcOffset", "nodeType": "YulIdentifier", "src": "11946:9:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "11937:3:1"}, "nodeType": "YulFunctionCall", "src": "11937:19:1"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "11931:5:1"}, "nodeType": "YulFunctionCall", "src": "11931:26:1"}], "functionName": {"name": "sstore", "nodeType": "YulIdentifier", "src": "11916:6:1"}, "nodeType": "YulFunctionCall", "src": "11916:42:1"}, "nodeType": "YulExpressionStatement", "src": "11916:42:1"}, {"nodeType": "YulAssignment", "src": "11975:24:1", "value": {"arguments": [{"name": "dstPtr", "nodeType": "YulIdentifier", "src": "11989:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "11997:1:1", "type": "", "value": "1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "11985:3:1"}, "nodeType": "YulFunctionCall", "src": "11985:14:1"}, "variableNames": [{"name": "dstPtr", "nodeType": "YulIdentifier", "src": "11975:6:1"}]}, {"nodeType": "YulAssignment", "src": "12016:40:1", "value": {"arguments": [{"name": "srcOffset", "nodeType": "YulIdentifier", "src": "12033:9:1"}, {"name": "srcOffset_1", "nodeType": "YulIdentifier", "src": "12044:11:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "12029:3:1"}, "nodeType": "YulFunctionCall", "src": "12029:27:1"}, "variableNames": [{"name": "srcOffset", "nodeType": "YulIdentifier", "src": "12016:9:1"}]}]}, "condition": {"arguments": [{"name": "i", "nodeType": "YulIdentifier", "src": "11845:1:1"}, {"name": "loopEnd", "nodeType": "YulIdentifier", "src": "11848:7:1"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "11842:2:1"}, "nodeType": "YulFunctionCall", "src": "11842:14:1"}, "nodeType": "YulForLoop", "post": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "11857:28:1", "statements": [{"nodeType": "YulAssignment", "src": "11859:24:1", "value": {"arguments": [{"name": "i", "nodeType": "YulIdentifier", "src": "11868:1:1"}, {"name": "srcOffset_1", "nodeType": "YulIdentifier", "src": "11871:11:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "11864:3:1"}, "nodeType": "YulFunctionCall", "src": "11864:19:1"}, "variableNames": [{"name": "i", "nodeType": "YulIdentifier", "src": "11859:1:1"}]}]}, "pre": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "11838:3:1", "statements": []}, "src": "11834:236:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "12118:166:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "12136:43:1", "value": {"arguments": [{"arguments": [{"name": "src", "nodeType": "YulIdentifier", "src": "12163:3:1"}, {"name": "srcOffset", "nodeType": "YulIdentifier", "src": "12168:9:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "12159:3:1"}, "nodeType": "YulFunctionCall", "src": "12159:19:1"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "12153:5:1"}, "nodeType": "YulFunctionCall", "src": "12153:26:1"}, "variables": [{"name": "lastValue", "nodeType": "YulTypedName", "src": "12140:9:1", "type": ""}]}, {"expression": {"arguments": [{"name": "dstPtr", "nodeType": "YulIdentifier", "src": "12203:6:1"}, {"arguments": [{"name": "lastValue", "nodeType": "YulIdentifier", "src": "12215:9:1"}, {"arguments": [{"arguments": [{"arguments": [{"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "12242:1:1", "type": "", "value": "3"}, {"name": "newLen", "nodeType": "YulIdentifier", "src": "12245:6:1"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "12238:3:1"}, "nodeType": "YulFunctionCall", "src": "12238:14:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "12254:3:1", "type": "", "value": "248"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "12234:3:1"}, "nodeType": "YulFunctionCall", "src": "12234:24:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "12264:1:1", "type": "", "value": "0"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "12260:3:1"}, "nodeType": "YulFunctionCall", "src": "12260:6:1"}], "functionName": {"name": "shr", "nodeType": "YulIdentifier", "src": "12230:3:1"}, "nodeType": "YulFunctionCall", "src": "12230:37:1"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "12226:3:1"}, "nodeType": "YulFunctionCall", "src": "12226:42:1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "12211:3:1"}, "nodeType": "YulFunctionCall", "src": "12211:58:1"}], "functionName": {"name": "sstore", "nodeType": "YulIdentifier", "src": "12196:6:1"}, "nodeType": "YulFunctionCall", "src": "12196:74:1"}, "nodeType": "YulExpressionStatement", "src": "12196:74:1"}]}, "condition": {"arguments": [{"name": "loopEnd", "nodeType": "YulIdentifier", "src": "12089:7:1"}, {"name": "newLen", "nodeType": "YulIdentifier", "src": "12098:6:1"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "12086:2:1"}, "nodeType": "YulFunctionCall", "src": "12086:19:1"}, "nodeType": "YulIf", "src": "12083:201:1"}, {"expression": {"arguments": [{"name": "slot", "nodeType": "YulIdentifier", "src": "12304:4:1"}, {"arguments": [{"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "12318:1:1", "type": "", "value": "1"}, {"name": "newLen", "nodeType": "YulIdentifier", "src": "12321:6:1"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "12314:3:1"}, "nodeType": "YulFunctionCall", "src": "12314:14:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "12330:1:1", "type": "", "value": "1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "12310:3:1"}, "nodeType": "YulFunctionCall", "src": "12310:22:1"}], "functionName": {"name": "sstore", "nodeType": "YulIdentifier", "src": "12297:6:1"}, "nodeType": "YulFunctionCall", "src": "12297:36:1"}, "nodeType": "YulExpressionStatement", "src": "12297:36:1"}]}, "nodeType": "YulCase", "src": "11680:663:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "11685:1:1", "type": "", "value": "1"}}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "12360:234:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "12374:14:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "12387:1:1", "type": "", "value": "0"}, "variables": [{"name": "value", "nodeType": "YulTypedName", "src": "12378:5:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "12423:67:1", "statements": [{"nodeType": "YulAssignment", "src": "12441:35:1", "value": {"arguments": [{"arguments": [{"name": "src", "nodeType": "YulIdentifier", "src": "12460:3:1"}, {"name": "srcOffset", "nodeType": "YulIdentifier", "src": "12465:9:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "12456:3:1"}, "nodeType": "YulFunctionCall", "src": "12456:19:1"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "12450:5:1"}, "nodeType": "YulFunctionCall", "src": "12450:26:1"}, "variableNames": [{"name": "value", "nodeType": "YulIdentifier", "src": "12441:5:1"}]}]}, "condition": {"name": "newLen", "nodeType": "YulIdentifier", "src": "12404:6:1"}, "nodeType": "YulIf", "src": "12401:89:1"}, {"expression": {"arguments": [{"name": "slot", "nodeType": "YulIdentifier", "src": "12510:4:1"}, {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "12569:5:1"}, {"name": "newLen", "nodeType": "YulIdentifier", "src": "12576:6:1"}], "functionName": {"name": "extract_used_part_and_set_length_of_short_byte_array", "nodeType": "YulIdentifier", "src": "12516:52:1"}, "nodeType": "YulFunctionCall", "src": "12516:67:1"}], "functionName": {"name": "sstore", "nodeType": "YulIdentifier", "src": "12503:6:1"}, "nodeType": "YulFunctionCall", "src": "12503:81:1"}, "nodeType": "YulExpressionStatement", "src": "12503:81:1"}]}, "nodeType": "YulCase", "src": "12352:242:1", "value": "default"}], "expression": {"arguments": [{"name": "newLen", "nodeType": "YulIdentifier", "src": "11660:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "11668:2:1", "type": "", "value": "31"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "11657:2:1"}, "nodeType": "YulFunctionCall", "src": "11657:14:1"}, "nodeType": "YulSwitch", "src": "11650:944:1"}]}, "name": "copy_byte_array_to_storage_from_t_string_memory_ptr_to_t_string_storage", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "slot", "nodeType": "YulTypedName", "src": "11329:4:1", "type": ""}, {"name": "src", "nodeType": "YulTypedName", "src": "11335:3:1", "type": ""}], "src": "11248:1352:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "12754:142:1", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "12771:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "12782:2:1", "type": "", "value": "64"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "12764:6:1"}, "nodeType": "YulFunctionCall", "src": "12764:21:1"}, "nodeType": "YulExpressionStatement", "src": "12764:21:1"}, {"nodeType": "YulAssignment", "src": "12794:53:1", "value": {"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "12820:6:1"}, {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "12832:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "12843:2:1", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "12828:3:1"}, "nodeType": "YulFunctionCall", "src": "12828:18:1"}], "functionName": {"name": "abi_encode_string", "nodeType": "YulIdentifier", "src": "12802:17:1"}, "nodeType": "YulFunctionCall", "src": "12802:45:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "12794:4:1"}]}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "12867:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "12878:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "12863:3:1"}, "nodeType": "YulFunctionCall", "src": "12863:18:1"}, {"name": "value1", "nodeType": "YulIdentifier", "src": "12883:6:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "12856:6:1"}, "nodeType": "YulFunctionCall", "src": "12856:34:1"}, "nodeType": "YulExpressionStatement", "src": "12856:34:1"}]}, "name": "abi_encode_tuple_t_string_memory_ptr_t_uint256__to_t_string_memory_ptr_t_uint256__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "12715:9:1", "type": ""}, {"name": "value1", "nodeType": "YulTypedName", "src": "12726:6:1", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "12734:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "12745:4:1", "type": ""}], "src": "12605:291:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "13075:168:1", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "13092:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "13103:2:1", "type": "", "value": "32"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "13085:6:1"}, "nodeType": "YulFunctionCall", "src": "13085:21:1"}, "nodeType": "YulExpressionStatement", "src": "13085:21:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "13126:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "13137:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "13122:3:1"}, "nodeType": "YulFunctionCall", "src": "13122:18:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "13142:2:1", "type": "", "value": "18"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "13115:6:1"}, "nodeType": "YulFunctionCall", "src": "13115:30:1"}, "nodeType": "YulExpressionStatement", "src": "13115:30:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "13165:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "13176:2:1", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "13161:3:1"}, "nodeType": "YulFunctionCall", "src": "13161:18:1"}, {"hexValue": "506f6c6c206973206e6f7420616374697665", "kind": "string", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "13181:20:1", "type": "", "value": "Poll is not active"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "13154:6:1"}, "nodeType": "YulFunctionCall", "src": "13154:48:1"}, "nodeType": "YulExpressionStatement", "src": "13154:48:1"}, {"nodeType": "YulAssignment", "src": "13211:26:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "13223:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "13234:2:1", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "13219:3:1"}, "nodeType": "YulFunctionCall", "src": "13219:18:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "13211:4:1"}]}]}, "name": "abi_encode_tuple_t_stringliteral_85be9a3a8b38ddaef4e16dab673e124fe2fa73224e04bf43989f8bc735b45740__to_t_string_memory_ptr__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "13052:9:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "13066:4:1", "type": ""}], "src": "12901:342:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "13422:163:1", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "13439:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "13450:2:1", "type": "", "value": "32"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "13432:6:1"}, "nodeType": "YulFunctionCall", "src": "13432:21:1"}, "nodeType": "YulExpressionStatement", "src": "13432:21:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "13473:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "13484:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "13469:3:1"}, "nodeType": "YulFunctionCall", "src": "13469:18:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "13489:2:1", "type": "", "value": "13"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "13462:6:1"}, "nodeType": "YulFunctionCall", "src": "13462:30:1"}, "nodeType": "YulExpressionStatement", "src": "13462:30:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "13512:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "13523:2:1", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "13508:3:1"}, "nodeType": "YulFunctionCall", "src": "13508:18:1"}, {"hexValue": "416c726561647920766f746564", "kind": "string", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "13528:15:1", "type": "", "value": "Already voted"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "13501:6:1"}, "nodeType": "YulFunctionCall", "src": "13501:43:1"}, "nodeType": "YulExpressionStatement", "src": "13501:43:1"}, {"nodeType": "YulAssignment", "src": "13553:26:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "13565:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "13576:2:1", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "13561:3:1"}, "nodeType": "YulFunctionCall", "src": "13561:18:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "13553:4:1"}]}]}, "name": "abi_encode_tuple_t_stringliteral_512fc59044d4f0722f9346c450973ffe8aac7aa1142e536739987018593c53b6__to_t_string_memory_ptr__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "13399:9:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "13413:4:1", "type": ""}], "src": "13248:337:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "13764:164:1", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "13781:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "13792:2:1", "type": "", "value": "32"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "13774:6:1"}, "nodeType": "YulFunctionCall", "src": "13774:21:1"}, "nodeType": "YulExpressionStatement", "src": "13774:21:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "13815:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "13826:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "13811:3:1"}, "nodeType": "YulFunctionCall", "src": "13811:18:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "13831:2:1", "type": "", "value": "14"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "13804:6:1"}, "nodeType": "YulFunctionCall", "src": "13804:30:1"}, "nodeType": "YulExpressionStatement", "src": "13804:30:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "13854:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "13865:2:1", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "13850:3:1"}, "nodeType": "YulFunctionCall", "src": "13850:18:1"}, {"hexValue": "496e76616c6964206f7074696f6e", "kind": "string", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "13870:16:1", "type": "", "value": "Invalid option"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "13843:6:1"}, "nodeType": "YulFunctionCall", "src": "13843:44:1"}, "nodeType": "YulExpressionStatement", "src": "13843:44:1"}, {"nodeType": "YulAssignment", "src": "13896:26:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "13908:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "13919:2:1", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "13904:3:1"}, "nodeType": "YulFunctionCall", "src": "13904:18:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "13896:4:1"}]}]}, "name": "abi_encode_tuple_t_stringliteral_47303e9cecb0d187ef38fc0ef78d94b0ad3bfd977aa49a6ef487d19e96bc3077__to_t_string_memory_ptr__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "13741:9:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "13755:4:1", "type": ""}], "src": "13590:338:1"}]}, "contents": "{\n    { }\n    function abi_decode_address(offset) -> value\n    {\n        value := calldataload(offset)\n        if iszero(eq(value, and(value, sub(shl(160, 1), 1)))) { revert(0, 0) }\n    }\n    function abi_decode_tuple_t_uint256t_address(headStart, dataEnd) -> value0, value1\n    {\n        if slt(sub(dataEnd, headStart), 64) { revert(0, 0) }\n        value0 := calldataload(headStart)\n        value1 := abi_decode_address(add(headStart, 32))\n    }\n    function abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed(headStart, value0) -> tail\n    {\n        tail := add(headStart, 32)\n        mstore(headStart, value0)\n    }\n    function abi_decode_tuple_t_uint256(headStart, dataEnd) -> value0\n    {\n        if slt(sub(dataEnd, headStart), 32) { revert(0, 0) }\n        value0 := calldataload(headStart)\n    }\n    function abi_encode_string(value, pos) -> end\n    {\n        let length := mload(value)\n        mstore(pos, length)\n        let i := 0\n        for { } lt(i, length) { i := add(i, 0x20) }\n        {\n            let _1 := 0x20\n            mstore(add(add(pos, i), _1), mload(add(add(value, i), _1)))\n        }\n        mstore(add(add(pos, length), 0x20), 0)\n        end := add(add(pos, and(add(length, 31), not(31))), 0x20)\n    }\n    function abi_encode_array_uint256_dyn(value, pos) -> end\n    {\n        let length := mload(value)\n        mstore(pos, length)\n        let _1 := 0x20\n        pos := add(pos, _1)\n        let srcPtr := add(value, _1)\n        let i := 0\n        for { } lt(i, length) { i := add(i, 1) }\n        {\n            mstore(pos, mload(srcPtr))\n            pos := add(pos, _1)\n            srcPtr := add(srcPtr, _1)\n        }\n        end := pos\n    }\n    function abi_encode_bool(value, pos)\n    {\n        mstore(pos, iszero(iszero(value)))\n    }\n    function abi_encode_address(value, pos)\n    {\n        mstore(pos, and(value, sub(shl(160, 1), 1)))\n    }\n    function abi_encode_tuple_t_string_memory_ptr_t_array$_t_string_memory_ptr_$dyn_memory_ptr_t_array$_t_uint256_$dyn_memory_ptr_t_bool_t_address_t_uint256__to_t_string_memory_ptr_t_array$_t_string_memory_ptr_$dyn_memory_ptr_t_array$_t_uint256_$dyn_memory_ptr_t_bool_t_address_t_uint256__fromStack_reversed(headStart, value5, value4, value3, value2, value1, value0) -> tail\n    {\n        mstore(headStart, 192)\n        let tail_1 := abi_encode_string(value0, add(headStart, 192))\n        let _1 := 32\n        mstore(add(headStart, _1), sub(tail_1, headStart))\n        let pos := tail_1\n        let length := mload(value1)\n        mstore(tail_1, length)\n        pos := add(tail_1, _1)\n        let tail_2 := add(add(tail_1, shl(5, length)), _1)\n        let srcPtr := add(value1, _1)\n        let i := 0\n        for { } lt(i, length) { i := add(i, 1) }\n        {\n            mstore(pos, add(sub(tail_2, tail_1), not(31)))\n            tail_2 := abi_encode_string(mload(srcPtr), tail_2)\n            srcPtr := add(srcPtr, _1)\n            pos := add(pos, _1)\n        }\n        mstore(add(headStart, 64), sub(tail_2, headStart))\n        tail := abi_encode_array_uint256_dyn(value2, tail_2)\n        abi_encode_bool(value3, add(headStart, 96))\n        abi_encode_address(value4, add(headStart, 128))\n        mstore(add(headStart, 160), value5)\n    }\n    function panic_error_0x41()\n    {\n        mstore(0, shl(224, 0x4e487b71))\n        mstore(4, 0x41)\n        revert(0, 0x24)\n    }\n    function allocate_memory(size) -> memPtr\n    {\n        memPtr := mload(64)\n        let newFreePtr := add(memPtr, and(add(size, 31), not(31)))\n        if or(gt(newFreePtr, 0xffffffffffffffff), lt(newFreePtr, memPtr)) { panic_error_0x41() }\n        mstore(64, newFreePtr)\n    }\n    function abi_decode_string(offset, end) -> array\n    {\n        if iszero(slt(add(offset, 0x1f), end)) { revert(0, 0) }\n        let _1 := calldataload(offset)\n        if gt(_1, 0xffffffffffffffff) { panic_error_0x41() }\n        let array_1 := allocate_memory(add(and(add(_1, 0x1f), not(31)), 0x20))\n        mstore(array_1, _1)\n        if gt(add(add(offset, _1), 0x20), end) { revert(0, 0) }\n        calldatacopy(add(array_1, 0x20), add(offset, 0x20), _1)\n        mstore(add(add(array_1, _1), 0x20), 0)\n        array := array_1\n    }\n    function abi_decode_tuple_t_string_memory_ptrt_array$_t_string_memory_ptr_$dyn_memory_ptr(headStart, dataEnd) -> value0, value1\n    {\n        if slt(sub(dataEnd, headStart), 64) { revert(0, 0) }\n        let offset := calldataload(headStart)\n        let _1 := 0xffffffffffffffff\n        if gt(offset, _1) { revert(0, 0) }\n        value0 := abi_decode_string(add(headStart, offset), dataEnd)\n        let _2 := 32\n        let offset_1 := calldataload(add(headStart, _2))\n        if gt(offset_1, _1) { revert(0, 0) }\n        let _3 := add(headStart, offset_1)\n        if iszero(slt(add(_3, 0x1f), dataEnd)) { revert(0, 0) }\n        let _4 := calldataload(_3)\n        if gt(_4, _1) { panic_error_0x41() }\n        let _5 := shl(5, _4)\n        let dst := allocate_memory(add(_5, _2))\n        let dst_1 := dst\n        mstore(dst, _4)\n        dst := add(dst, _2)\n        let srcEnd := add(add(_3, _5), _2)\n        if gt(srcEnd, dataEnd) { revert(0, 0) }\n        let src := add(_3, _2)\n        for { } lt(src, srcEnd) { src := add(src, _2) }\n        {\n            let innerOffset := calldataload(src)\n            if gt(innerOffset, _1)\n            {\n                let _6 := 0\n                revert(_6, _6)\n            }\n            mstore(dst, abi_decode_string(add(add(_3, innerOffset), _2), dataEnd))\n            dst := add(dst, _2)\n        }\n        value1 := dst_1\n    }\n    function abi_decode_tuple_t_addresst_uint256(headStart, dataEnd) -> value0, value1\n    {\n        if slt(sub(dataEnd, headStart), 64) { revert(0, 0) }\n        value0 := abi_decode_address(headStart)\n        value1 := calldataload(add(headStart, 32))\n    }\n    function abi_encode_tuple_t_string_memory_ptr_t_bool_t_address_t_uint256__to_t_string_memory_ptr_t_bool_t_address_t_uint256__fromStack_reversed(headStart, value3, value2, value1, value0) -> tail\n    {\n        mstore(headStart, 128)\n        tail := abi_encode_string(value0, add(headStart, 128))\n        mstore(add(headStart, 32), iszero(iszero(value1)))\n        mstore(add(headStart, 64), and(value2, sub(shl(160, 1), 1)))\n        mstore(add(headStart, 96), value3)\n    }\n    function abi_decode_tuple_t_uint256t_uint256(headStart, dataEnd) -> value0, value1\n    {\n        if slt(sub(dataEnd, headStart), 64) { revert(0, 0) }\n        value0 := calldataload(headStart)\n        value1 := calldataload(add(headStart, 32))\n    }\n    function abi_encode_tuple_t_bool__to_t_bool__fromStack_reversed(headStart, value0) -> tail\n    {\n        tail := add(headStart, 32)\n        mstore(headStart, iszero(iszero(value0)))\n    }\n    function abi_decode_tuple_t_address(headStart, dataEnd) -> value0\n    {\n        if slt(sub(dataEnd, headStart), 32) { revert(0, 0) }\n        value0 := abi_decode_address(headStart)\n    }\n    function abi_encode_tuple_t_array$_t_uint256_$dyn_memory_ptr__to_t_array$_t_uint256_$dyn_memory_ptr__fromStack_reversed(headStart, value0) -> tail\n    {\n        mstore(headStart, 32)\n        tail := abi_encode_array_uint256_dyn(value0, add(headStart, 32))\n    }\n    function abi_encode_tuple_t_stringliteral_a570800e348d215f0f8e7aaa1afa84c103bca1d27fa74d7cf9c25b83f991280b__to_t_string_memory_ptr__fromStack_reversed(headStart) -> tail\n    {\n        mstore(headStart, 32)\n        mstore(add(headStart, 32), 19)\n        mstore(add(headStart, 64), \"Poll does not exist\")\n        tail := add(headStart, 96)\n    }\n    function panic_error_0x32()\n    {\n        mstore(0, shl(224, 0x4e487b71))\n        mstore(4, 0x32)\n        revert(0, 0x24)\n    }\n    function abi_encode_tuple_t_stringliteral_ec1b155bf1042601d514070206494c49df2fdba4a1da92fad635a0f6e770613f__to_t_string_memory_ptr__fromStack_reversed(headStart) -> tail\n    {\n        mstore(headStart, 32)\n        mstore(add(headStart, 32), 18)\n        mstore(add(headStart, 64), \"User has not voted\")\n        tail := add(headStart, 96)\n    }\n    function extract_byte_array_length(data) -> length\n    {\n        length := shr(1, data)\n        let outOfPlaceEncoding := and(data, 1)\n        if iszero(outOfPlaceEncoding) { length := and(length, 0x7f) }\n        if eq(outOfPlaceEncoding, lt(length, 32))\n        {\n            mstore(0, shl(224, 0x4e487b71))\n            mstore(4, 0x22)\n            revert(0, 0x24)\n        }\n    }\n    function abi_encode_tuple_t_stringliteral_39d63f22c983e0a835c80b26c00e97ac46e50731b96804551d851bf715b5fd2c__to_t_string_memory_ptr__fromStack_reversed(headStart) -> tail\n    {\n        mstore(headStart, 32)\n        mstore(add(headStart, 32), 30)\n        mstore(add(headStart, 64), \"Only creator can toggle status\")\n        tail := add(headStart, 96)\n    }\n    function abi_encode_tuple_t_stringliteral_e91f32e8485e8cc61ee75db81581675d5614973e773b4b2d6682176c252ce441__to_t_string_memory_ptr__fromStack_reversed(headStart) -> tail\n    {\n        mstore(headStart, 32)\n        mstore(add(headStart, 32), 24)\n        mstore(add(headStart, 64), \"Question cannot be empty\")\n        tail := add(headStart, 96)\n    }\n    function abi_encode_tuple_t_stringliteral_8df94984f0986c4b0f652788cbe872845edfb9f154cbd9bb376c68bda91b6cbd__to_t_string_memory_ptr__fromStack_reversed(headStart) -> tail\n    {\n        mstore(headStart, 32)\n        mstore(add(headStart, 32), 27)\n        mstore(add(headStart, 64), \"At least 2 options required\")\n        tail := add(headStart, 96)\n    }\n    function abi_encode_tuple_t_stringliteral_b5c2a862288076e78ffb9f7b2c2d4242ebde1778466579712ffc2d1471c123ec__to_t_string_memory_ptr__fromStack_reversed(headStart) -> tail\n    {\n        mstore(headStart, 32)\n        mstore(add(headStart, 32), 16)\n        mstore(add(headStart, 64), \"Too many options\")\n        tail := add(headStart, 96)\n    }\n    function abi_encode_tuple_t_stringliteral_e951eb8529546f8abd8d997e0753f069f65b139bcb89559e3d3f221bef3b55a5__to_t_string_memory_ptr__fromStack_reversed(headStart) -> tail\n    {\n        mstore(headStart, 32)\n        mstore(add(headStart, 32), 22)\n        mstore(add(headStart, 64), \"Option cannot be empty\")\n        tail := add(headStart, 96)\n    }\n    function increment_t_uint256(value) -> ret\n    {\n        if eq(value, not(0))\n        {\n            mstore(0, shl(224, 0x4e487b71))\n            mstore(4, 0x11)\n            revert(0, 0x24)\n        }\n        ret := add(value, 1)\n    }\n    function array_dataslot_string_storage(ptr) -> data\n    {\n        mstore(0, ptr)\n        data := keccak256(0, 0x20)\n    }\n    function clean_up_bytearray_end_slots_string_storage(array, len, startIndex)\n    {\n        if gt(len, 31)\n        {\n            let _1 := 0\n            mstore(_1, array)\n            let data := keccak256(_1, 0x20)\n            let deleteStart := add(data, shr(5, add(startIndex, 31)))\n            if lt(startIndex, 0x20) { deleteStart := data }\n            let _2 := add(data, shr(5, add(len, 31)))\n            let start := deleteStart\n            for { } lt(start, _2) { start := add(start, 1) }\n            { sstore(start, _1) }\n        }\n    }\n    function extract_used_part_and_set_length_of_short_byte_array(data, len) -> used\n    {\n        used := or(and(data, not(shr(shl(3, len), not(0)))), shl(1, len))\n    }\n    function copy_byte_array_to_storage_from_t_string_memory_ptr_to_t_string_storage(slot, src)\n    {\n        let newLen := mload(src)\n        if gt(newLen, 0xffffffffffffffff) { panic_error_0x41() }\n        clean_up_bytearray_end_slots_string_storage(slot, extract_byte_array_length(sload(slot)), newLen)\n        let srcOffset := 0\n        let srcOffset_1 := 0x20\n        srcOffset := srcOffset_1\n        switch gt(newLen, 31)\n        case 1 {\n            let loopEnd := and(newLen, not(31))\n            let dstPtr := array_dataslot_string_storage(slot)\n            let i := 0\n            for { } lt(i, loopEnd) { i := add(i, srcOffset_1) }\n            {\n                sstore(dstPtr, mload(add(src, srcOffset)))\n                dstPtr := add(dstPtr, 1)\n                srcOffset := add(srcOffset, srcOffset_1)\n            }\n            if lt(loopEnd, newLen)\n            {\n                let lastValue := mload(add(src, srcOffset))\n                sstore(dstPtr, and(lastValue, not(shr(and(shl(3, newLen), 248), not(0)))))\n            }\n            sstore(slot, add(shl(1, newLen), 1))\n        }\n        default {\n            let value := 0\n            if newLen\n            {\n                value := mload(add(src, srcOffset))\n            }\n            sstore(slot, extract_used_part_and_set_length_of_short_byte_array(value, newLen))\n        }\n    }\n    function abi_encode_tuple_t_string_memory_ptr_t_uint256__to_t_string_memory_ptr_t_uint256__fromStack_reversed(headStart, value1, value0) -> tail\n    {\n        mstore(headStart, 64)\n        tail := abi_encode_string(value0, add(headStart, 64))\n        mstore(add(headStart, 32), value1)\n    }\n    function abi_encode_tuple_t_stringliteral_85be9a3a8b38ddaef4e16dab673e124fe2fa73224e04bf43989f8bc735b45740__to_t_string_memory_ptr__fromStack_reversed(headStart) -> tail\n    {\n        mstore(headStart, 32)\n        mstore(add(headStart, 32), 18)\n        mstore(add(headStart, 64), \"Poll is not active\")\n        tail := add(headStart, 96)\n    }\n    function abi_encode_tuple_t_stringliteral_512fc59044d4f0722f9346c450973ffe8aac7aa1142e536739987018593c53b6__to_t_string_memory_ptr__fromStack_reversed(headStart) -> tail\n    {\n        mstore(headStart, 32)\n        mstore(add(headStart, 32), 13)\n        mstore(add(headStart, 64), \"Already voted\")\n        tail := add(headStart, 96)\n    }\n    function abi_encode_tuple_t_stringliteral_47303e9cecb0d187ef38fc0ef78d94b0ad3bfd977aa49a6ef487d19e96bc3077__to_t_string_memory_ptr__fromStack_reversed(headStart) -> tail\n    {\n        mstore(headStart, 32)\n        mstore(add(headStart, 32), 14)\n        mstore(add(headStart, 64), \"Invalid option\")\n        tail := add(headStart, 96)\n    }\n}", "id": 1, "language": "<PERSON>l", "name": "#utility.yul"}], "immutableReferences": {}, "linkReferences": {}, "object": "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", "opcodes": "PUSH1 0x80 PUSH1 0x40 MSTORE CALLVALUE DUP1 ISZERO PUSH2 0x10 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH1 0x4 CALLDATASIZE LT PUSH2 0xA9 JUMPI PUSH1 0x0 CALLDATALOAD PUSH1 0xE0 SHR DUP1 PUSH4 0x8439E976 GT PUSH2 0x71 JUMPI DUP1 PUSH4 0x8439E976 EQ PUSH2 0x129 JUMPI DUP1 PUSH4 0x9207891D EQ PUSH2 0x13C JUMPI DUP1 PUSH4 0xAC2F0074 EQ PUSH2 0x145 JUMPI DUP1 PUSH4 0xB384ABEF EQ PUSH2 0x168 JUMPI DUP1 PUSH4 0xDC296AE1 EQ PUSH2 0x17B JUMPI DUP1 PUSH4 0xEC81A17A EQ PUSH2 0x19E JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP1 PUSH4 0x3C7881A EQ PUSH2 0xAE JUMPI DUP1 PUSH4 0x11A52746 EQ PUSH2 0xD4 JUMPI DUP1 PUSH4 0x1A8CBCAA EQ PUSH2 0xDC JUMPI DUP1 PUSH4 0x251E02D7 EQ PUSH2 0x101 JUMPI DUP1 PUSH4 0x7E3D71C3 EQ PUSH2 0x116 JUMPI JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH2 0xC1 PUSH2 0xBC CALLDATASIZE PUSH1 0x4 PUSH2 0xE6F JUMP JUMPDEST PUSH2 0x1BE JUMP JUMPDEST PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE PUSH1 0x20 ADD JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST PUSH1 0x0 SLOAD PUSH2 0xC1 JUMP JUMPDEST PUSH2 0xEF PUSH2 0xEA CALLDATASIZE PUSH1 0x4 PUSH2 0xE9B JUMP JUMPDEST PUSH2 0x2B0 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0xCB SWAP7 SWAP6 SWAP5 SWAP4 SWAP3 SWAP2 SWAP1 PUSH2 0xF35 JUMP JUMPDEST PUSH2 0x114 PUSH2 0x10F CALLDATASIZE PUSH1 0x4 PUSH2 0xE9B JUMP JUMPDEST PUSH2 0x4FA JUMP JUMPDEST STOP JUMPDEST PUSH2 0xC1 PUSH2 0x124 CALLDATASIZE PUSH1 0x4 PUSH2 0x1095 JUMP JUMPDEST PUSH2 0x67A JUMP JUMPDEST PUSH2 0xC1 PUSH2 0x137 CALLDATASIZE PUSH1 0x4 PUSH2 0x117C JUMP JUMPDEST PUSH2 0x93B JUMP JUMPDEST PUSH2 0xC1 PUSH1 0x1 SLOAD DUP2 JUMP JUMPDEST PUSH2 0x158 PUSH2 0x153 CALLDATASIZE PUSH1 0x4 PUSH2 0xE9B JUMP JUMPDEST PUSH2 0x96C JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0xCB SWAP5 SWAP4 SWAP3 SWAP2 SWAP1 PUSH2 0x11A6 JUMP JUMPDEST PUSH2 0x114 PUSH2 0x176 CALLDATASIZE PUSH1 0x4 PUSH2 0x11DE JUMP JUMPDEST PUSH2 0xA42 JUMP JUMPDEST PUSH2 0x18E PUSH2 0x189 CALLDATASIZE PUSH1 0x4 PUSH2 0xE6F JUMP JUMPDEST PUSH2 0xC6F JUMP JUMPDEST PUSH1 0x40 MLOAD SWAP1 ISZERO ISZERO DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0xCB JUMP JUMPDEST PUSH2 0x1B1 PUSH2 0x1AC CALLDATASIZE PUSH1 0x4 PUSH2 0x1200 JUMP JUMPDEST PUSH2 0xCDB JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0xCB SWAP2 SWAP1 PUSH2 0x1222 JUMP JUMPDEST PUSH1 0x0 DUP1 SLOAD DUP4 SWAP1 DUP2 LT PUSH2 0x1EB JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH2 0x1E2 SWAP1 PUSH2 0x1235 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH1 0x0 DUP5 DUP2 SLOAD DUP2 LT PUSH2 0x1FE JUMPI PUSH2 0x1FE PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 DUP1 DUP4 KECCAK256 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP8 AND DUP5 MSTORE PUSH1 0x3 PUSH1 0x7 SWAP1 SWAP4 MUL ADD SWAP2 SWAP1 SWAP2 ADD SWAP1 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD PUSH1 0xFF AND PUSH2 0x26B JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x12 PUSH1 0x24 DUP3 ADD MSTORE PUSH18 0x155CD95C881A185CC81B9BDD081D9BDD1959 PUSH1 0x72 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x1E2 JUMP JUMPDEST PUSH1 0x0 DUP5 DUP2 SLOAD DUP2 LT PUSH2 0x27E JUMPI PUSH2 0x27E PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 DUP1 DUP4 KECCAK256 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP8 AND DUP5 MSTORE PUSH1 0x4 PUSH1 0x7 SWAP1 SWAP4 MUL ADD SWAP2 SWAP1 SWAP2 ADD SWAP1 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x60 DUP1 PUSH1 0x60 PUSH1 0x0 DUP1 PUSH1 0x0 DUP7 PUSH1 0x0 DUP1 SLOAD SWAP1 POP DUP2 LT PUSH2 0x2DF JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH2 0x1E2 SWAP1 PUSH2 0x1235 JUMP JUMPDEST PUSH1 0x0 DUP1 DUP10 DUP2 SLOAD DUP2 LT PUSH2 0x2F3 JUMPI PUSH2 0x2F3 PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 SWAP1 SWAP2 KECCAK256 PUSH1 0x7 SWAP1 SWAP2 MUL ADD PUSH1 0x5 DUP2 ADD SLOAD PUSH1 0x6 DUP3 ADD SLOAD DUP3 SLOAD SWAP3 SWAP4 POP DUP4 SWAP3 PUSH1 0x1 DUP5 ADD SWAP3 PUSH1 0x2 DUP6 ADD SWAP3 PUSH1 0xFF DUP3 AND SWAP3 PUSH2 0x100 SWAP1 SWAP3 DIV PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND SWAP2 DUP7 SWAP1 PUSH2 0x33E SWAP1 PUSH2 0x1278 JUMP JUMPDEST DUP1 PUSH1 0x1F ADD PUSH1 0x20 DUP1 SWAP2 DIV MUL PUSH1 0x20 ADD PUSH1 0x40 MLOAD SWAP1 DUP2 ADD PUSH1 0x40 MSTORE DUP1 SWAP3 SWAP2 SWAP1 DUP2 DUP2 MSTORE PUSH1 0x20 ADD DUP3 DUP1 SLOAD PUSH2 0x36A SWAP1 PUSH2 0x1278 JUMP JUMPDEST DUP1 ISZERO PUSH2 0x3B7 JUMPI DUP1 PUSH1 0x1F LT PUSH2 0x38C JUMPI PUSH2 0x100 DUP1 DUP4 SLOAD DIV MUL DUP4 MSTORE SWAP2 PUSH1 0x20 ADD SWAP2 PUSH2 0x3B7 JUMP JUMPDEST DUP3 ADD SWAP2 SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 JUMPDEST DUP2 SLOAD DUP2 MSTORE SWAP1 PUSH1 0x1 ADD SWAP1 PUSH1 0x20 ADD DUP1 DUP4 GT PUSH2 0x39A JUMPI DUP3 SWAP1 SUB PUSH1 0x1F AND DUP3 ADD SWAP2 JUMPDEST POP POP POP POP POP SWAP6 POP DUP5 DUP1 SLOAD DUP1 PUSH1 0x20 MUL PUSH1 0x20 ADD PUSH1 0x40 MLOAD SWAP1 DUP2 ADD PUSH1 0x40 MSTORE DUP1 SWAP3 SWAP2 SWAP1 DUP2 DUP2 MSTORE PUSH1 0x20 ADD PUSH1 0x0 SWAP1 JUMPDEST DUP3 DUP3 LT ISZERO PUSH2 0x48B JUMPI DUP4 DUP3 SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 ADD DUP1 SLOAD PUSH2 0x3FE SWAP1 PUSH2 0x1278 JUMP JUMPDEST DUP1 PUSH1 0x1F ADD PUSH1 0x20 DUP1 SWAP2 DIV MUL PUSH1 0x20 ADD PUSH1 0x40 MLOAD SWAP1 DUP2 ADD PUSH1 0x40 MSTORE DUP1 SWAP3 SWAP2 SWAP1 DUP2 DUP2 MSTORE PUSH1 0x20 ADD DUP3 DUP1 SLOAD PUSH2 0x42A SWAP1 PUSH2 0x1278 JUMP JUMPDEST DUP1 ISZERO PUSH2 0x477 JUMPI DUP1 PUSH1 0x1F LT PUSH2 0x44C JUMPI PUSH2 0x100 DUP1 DUP4 SLOAD DIV MUL DUP4 MSTORE SWAP2 PUSH1 0x20 ADD SWAP2 PUSH2 0x477 JUMP JUMPDEST DUP3 ADD SWAP2 SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 JUMPDEST DUP2 SLOAD DUP2 MSTORE SWAP1 PUSH1 0x1 ADD SWAP1 PUSH1 0x20 ADD DUP1 DUP4 GT PUSH2 0x45A JUMPI DUP3 SWAP1 SUB PUSH1 0x1F AND DUP3 ADD SWAP2 JUMPDEST POP POP POP POP POP DUP2 MSTORE PUSH1 0x20 ADD SWAP1 PUSH1 0x1 ADD SWAP1 PUSH2 0x3DF JUMP JUMPDEST POP POP POP POP SWAP5 POP DUP4 DUP1 SLOAD DUP1 PUSH1 0x20 MUL PUSH1 0x20 ADD PUSH1 0x40 MLOAD SWAP1 DUP2 ADD PUSH1 0x40 MSTORE DUP1 SWAP3 SWAP2 SWAP1 DUP2 DUP2 MSTORE PUSH1 0x20 ADD DUP3 DUP1 SLOAD DUP1 ISZERO PUSH2 0x4DC JUMPI PUSH1 0x20 MUL DUP3 ADD SWAP2 SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 JUMPDEST DUP2 SLOAD DUP2 MSTORE PUSH1 0x20 ADD SWAP1 PUSH1 0x1 ADD SWAP1 DUP1 DUP4 GT PUSH2 0x4C8 JUMPI JUMPDEST POP POP POP POP POP SWAP4 POP SWAP8 POP SWAP8 POP SWAP8 POP SWAP8 POP SWAP8 POP SWAP8 POP POP POP SWAP2 SWAP4 SWAP6 POP SWAP2 SWAP4 SWAP6 JUMP JUMPDEST PUSH1 0x0 SLOAD DUP2 SWAP1 DUP2 LT PUSH2 0x51D JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH2 0x1E2 SWAP1 PUSH2 0x1235 JUMP JUMPDEST CALLER PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH1 0x0 DUP4 DUP2 SLOAD DUP2 LT PUSH2 0x53A JUMPI PUSH2 0x53A PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 SWAP1 SWAP2 KECCAK256 PUSH1 0x7 SWAP1 SWAP2 MUL ADD PUSH1 0x5 ADD SLOAD PUSH2 0x100 SWAP1 DIV PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND EQ PUSH2 0x5A9 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x1E PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x4F6E6C792063726561746F722063616E20746F67676C65207374617475730000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x1E2 JUMP JUMPDEST PUSH1 0x0 DUP3 DUP2 SLOAD DUP2 LT PUSH2 0x5BC JUMPI PUSH2 0x5BC PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 DUP3 KECCAK256 PUSH1 0x5 PUSH1 0x7 SWAP1 SWAP3 MUL ADD ADD SLOAD DUP2 SLOAD PUSH1 0xFF SWAP1 SWAP2 AND ISZERO SWAP2 SWAP1 DUP5 SWAP1 DUP2 LT PUSH2 0x5E9 JUMPI PUSH2 0x5E9 PUSH2 0x1262 JUMP JUMPDEST SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x7 MUL ADD PUSH1 0x5 ADD PUSH1 0x0 PUSH2 0x100 EXP DUP2 SLOAD DUP2 PUSH1 0xFF MUL NOT AND SWAP1 DUP4 ISZERO ISZERO MUL OR SWAP1 SSTORE POP DUP2 PUSH32 0xB7B8C0761D078843A3E84D05266AB3ECA7A488CD293CCE2EAC1441C9287D4B53 PUSH1 0x0 DUP5 DUP2 SLOAD DUP2 LT PUSH2 0x646 JUMPI PUSH2 0x646 PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 SWAP1 SWAP2 KECCAK256 PUSH1 0x5 PUSH1 0x7 SWAP1 SWAP3 MUL ADD ADD SLOAD PUSH1 0x40 MLOAD PUSH2 0x66E SWAP2 PUSH1 0xFF AND ISZERO ISZERO DUP2 MSTORE PUSH1 0x20 ADD SWAP1 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG2 POP POP JUMP JUMPDEST PUSH1 0x0 DUP3 DUP3 PUSH1 0x0 DUP3 MLOAD GT PUSH2 0x6CF JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x18 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x5175657374696F6E2063616E6E6F7420626520656D7074790000000000000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x1E2 JUMP JUMPDEST PUSH1 0x2 DUP2 MLOAD LT ISZERO PUSH2 0x721 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x1B PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x4174206C656173742032206F7074696F6E732072657175697265640000000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x1E2 JUMP JUMPDEST PUSH1 0xA DUP2 MLOAD GT ISZERO PUSH2 0x766 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x10 PUSH1 0x24 DUP3 ADD MSTORE PUSH16 0x546F6F206D616E79206F7074696F6E73 PUSH1 0x80 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x1E2 JUMP JUMPDEST PUSH1 0x0 JUMPDEST DUP2 MLOAD DUP2 LT ISZERO PUSH2 0x7E7 JUMPI PUSH1 0x0 DUP3 DUP3 DUP2 MLOAD DUP2 LT PUSH2 0x786 JUMPI PUSH2 0x786 PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x20 MUL PUSH1 0x20 ADD ADD MLOAD MLOAD GT PUSH2 0x7D5 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x16 PUSH1 0x24 DUP3 ADD MSTORE PUSH22 0x4F7074696F6E2063616E6E6F7420626520656D707479 PUSH1 0x50 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x1E2 JUMP JUMPDEST DUP1 PUSH2 0x7DF DUP2 PUSH2 0x12B2 JUMP JUMPDEST SWAP2 POP POP PUSH2 0x769 JUMP JUMPDEST POP PUSH1 0x0 DUP1 SLOAD PUSH1 0x1 DUP2 ADD DUP1 DUP4 SSTORE DUP3 DUP1 MSTORE SWAP1 SWAP5 POP DUP2 SWAP1 DUP6 SWAP1 DUP2 LT PUSH2 0x80B JUMPI PUSH2 0x80B PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 SWAP1 SWAP2 KECCAK256 PUSH1 0x7 SWAP1 SWAP2 MUL ADD SWAP1 POP DUP1 PUSH2 0x828 DUP8 DUP3 PUSH2 0x1328 JUMP JUMPDEST POP DUP5 MLOAD PUSH2 0x83E SWAP1 PUSH1 0x1 DUP4 ADD SWAP1 PUSH1 0x20 DUP9 ADD SWAP1 PUSH2 0xD47 JUMP JUMPDEST POP DUP5 MLOAD PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT ISZERO PUSH2 0x859 JUMPI PUSH2 0x859 PUSH2 0xFDE JUMP JUMPDEST PUSH1 0x40 MLOAD SWAP1 DUP1 DUP3 MSTORE DUP1 PUSH1 0x20 MUL PUSH1 0x20 ADD DUP3 ADD PUSH1 0x40 MSTORE DUP1 ISZERO PUSH2 0x882 JUMPI DUP2 PUSH1 0x20 ADD PUSH1 0x20 DUP3 MUL DUP1 CALLDATASIZE DUP4 CALLDATACOPY ADD SWAP1 POP JUMPDEST POP DUP1 MLOAD PUSH2 0x899 SWAP2 PUSH1 0x2 DUP5 ADD SWAP2 PUSH1 0x20 SWAP1 SWAP2 ADD SWAP1 PUSH2 0xD9D JUMP JUMPDEST POP PUSH1 0x5 DUP2 ADD DUP1 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA8 SHL SUB NOT SWAP1 SWAP2 AND PUSH2 0x100 CALLER MUL OR DUP2 OR SWAP1 SWAP2 SSTORE TIMESTAMP PUSH1 0x6 DUP4 ADD SSTORE DUP1 SLOAD SWAP1 PUSH1 0x0 PUSH2 0x8CD DUP4 PUSH2 0x12B2 JUMP JUMPDEST SWAP1 SWAP2 SSTORE POP POP CALLER PUSH1 0x0 DUP2 DUP2 MSTORE PUSH1 0x2 PUSH1 0x20 SWAP1 DUP2 MSTORE PUSH1 0x40 DUP1 DUP4 KECCAK256 DUP1 SLOAD PUSH1 0x1 DUP2 ADD DUP3 SSTORE SWAP1 DUP5 MSTORE SWAP2 SWAP1 SWAP3 KECCAK256 ADD DUP7 SWAP1 SSTORE DUP7 MLOAD SWAP1 MLOAD DUP7 SWAP2 PUSH32 0x9DBAFD4116DCEAD87280B4BAD16E92C851A647FBF87A77E05CF6B29C8FCDC33C SWAP2 PUSH2 0x92A SWAP2 DUP12 SWAP2 PUSH2 0x13E8 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG3 POP POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x2 PUSH1 0x20 MSTORE DUP2 PUSH1 0x0 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 DUP2 DUP2 SLOAD DUP2 LT PUSH2 0x957 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 ADD PUSH1 0x0 SWAP2 POP SWAP2 POP POP SLOAD DUP2 JUMP JUMPDEST PUSH1 0x0 DUP2 DUP2 SLOAD DUP2 LT PUSH2 0x97C JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x7 MUL ADD PUSH1 0x0 SWAP2 POP SWAP1 POP DUP1 PUSH1 0x0 ADD DUP1 SLOAD PUSH2 0x99F SWAP1 PUSH2 0x1278 JUMP JUMPDEST DUP1 PUSH1 0x1F ADD PUSH1 0x20 DUP1 SWAP2 DIV MUL PUSH1 0x20 ADD PUSH1 0x40 MLOAD SWAP1 DUP2 ADD PUSH1 0x40 MSTORE DUP1 SWAP3 SWAP2 SWAP1 DUP2 DUP2 MSTORE PUSH1 0x20 ADD DUP3 DUP1 SLOAD PUSH2 0x9CB SWAP1 PUSH2 0x1278 JUMP JUMPDEST DUP1 ISZERO PUSH2 0xA18 JUMPI DUP1 PUSH1 0x1F LT PUSH2 0x9ED JUMPI PUSH2 0x100 DUP1 DUP4 SLOAD DIV MUL DUP4 MSTORE SWAP2 PUSH1 0x20 ADD SWAP2 PUSH2 0xA18 JUMP JUMPDEST DUP3 ADD SWAP2 SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 JUMPDEST DUP2 SLOAD DUP2 MSTORE SWAP1 PUSH1 0x1 ADD SWAP1 PUSH1 0x20 ADD DUP1 DUP4 GT PUSH2 0x9FB JUMPI DUP3 SWAP1 SUB PUSH1 0x1F AND DUP3 ADD SWAP2 JUMPDEST POP POP POP POP PUSH1 0x5 DUP4 ADD SLOAD PUSH1 0x6 SWAP1 SWAP4 ADD SLOAD SWAP2 SWAP3 PUSH1 0xFF DUP2 AND SWAP3 PUSH2 0x100 SWAP1 SWAP2 DIV PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND SWAP2 POP DUP5 JUMP JUMPDEST PUSH1 0x0 SLOAD DUP3 SWAP1 DUP2 LT PUSH2 0xA65 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH2 0x1E2 SWAP1 PUSH2 0x1235 JUMP JUMPDEST DUP3 PUSH1 0x0 DUP2 DUP2 SLOAD DUP2 LT PUSH2 0xA79 JUMPI PUSH2 0xA79 PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 SWAP1 SWAP2 KECCAK256 PUSH1 0x5 PUSH1 0x7 SWAP1 SWAP3 MUL ADD ADD SLOAD PUSH1 0xFF AND PUSH2 0xAD1 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x12 PUSH1 0x24 DUP3 ADD MSTORE PUSH18 0x506F6C6C206973206E6F7420616374697665 PUSH1 0x70 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x1E2 JUMP JUMPDEST DUP4 PUSH1 0x0 DUP2 DUP2 SLOAD DUP2 LT PUSH2 0xAE5 JUMPI PUSH2 0xAE5 PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 DUP1 DUP4 KECCAK256 CALLER DUP5 MSTORE PUSH1 0x3 PUSH1 0x7 SWAP1 SWAP4 MUL ADD SWAP2 SWAP1 SWAP2 ADD SWAP1 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD PUSH1 0xFF AND ISZERO PUSH2 0xB45 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0xD PUSH1 0x24 DUP3 ADD MSTORE PUSH13 0x105B1C9958591E481D9BDD1959 PUSH1 0x9A SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x1E2 JUMP JUMPDEST DUP5 DUP5 PUSH1 0x0 DUP3 DUP2 SLOAD DUP2 LT PUSH2 0xB5A JUMPI PUSH2 0xB5A PUSH2 0x1262 JUMP JUMPDEST SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x7 MUL ADD PUSH1 0x1 ADD DUP1 SLOAD SWAP1 POP DUP2 LT PUSH2 0xBAE JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0xE PUSH1 0x24 DUP3 ADD MSTORE PUSH14 0x24B73B30B634B21037B83A34B7B7 PUSH1 0x91 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x1E2 JUMP JUMPDEST PUSH1 0x0 DUP1 DUP9 DUP2 SLOAD DUP2 LT PUSH2 0xBC2 JUMPI PUSH2 0xBC2 PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 DUP1 DUP4 KECCAK256 CALLER DUP5 MSTORE PUSH1 0x3 PUSH1 0x7 SWAP1 SWAP4 MUL ADD SWAP2 DUP3 ADD DUP2 MSTORE PUSH1 0x40 DUP1 DUP5 KECCAK256 DUP1 SLOAD PUSH1 0xFF NOT AND PUSH1 0x1 OR SWAP1 SSTORE PUSH1 0x4 DUP4 ADD SWAP1 SWAP2 MSTORE SWAP1 SWAP2 KECCAK256 DUP9 SWAP1 SSTORE PUSH1 0x2 DUP2 ADD DUP1 SLOAD SWAP2 SWAP3 POP SWAP1 DUP9 SWAP1 DUP2 LT PUSH2 0xC13 JUMPI PUSH2 0xC13 PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 DUP3 KECCAK256 ADD DUP1 SLOAD SWAP2 PUSH2 0xC29 DUP4 PUSH2 0x12B2 JUMP JUMPDEST SWAP1 SWAP2 SSTORE POP POP PUSH1 0x40 MLOAD DUP8 DUP2 MSTORE CALLER SWAP1 DUP10 SWAP1 PUSH32 0x2ACCE567DECA3AABF56327ADBB4524BD5318936EAEFA69E3A5208FFDA0CFEC09 SWAP1 PUSH1 0x20 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG3 POP POP POP POP POP POP POP POP JUMP JUMPDEST PUSH1 0x0 DUP1 SLOAD DUP4 SWAP1 DUP2 LT PUSH2 0xC93 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH2 0x1E2 SWAP1 PUSH2 0x1235 JUMP JUMPDEST PUSH1 0x0 DUP5 DUP2 SLOAD DUP2 LT PUSH2 0xCA6 JUMPI PUSH2 0xCA6 PUSH2 0x1262 JUMP JUMPDEST PUSH1 0x0 SWAP2 DUP3 MSTORE PUSH1 0x20 DUP1 DUP4 KECCAK256 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP8 AND DUP5 MSTORE PUSH1 0x3 PUSH1 0x7 SWAP1 SWAP4 MUL ADD SWAP2 SWAP1 SWAP2 ADD SWAP1 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD PUSH1 0xFF AND SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP2 AND PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH1 0x2 PUSH1 0x20 SWAP1 DUP2 MSTORE PUSH1 0x40 SWAP2 DUP3 SWAP1 KECCAK256 DUP1 SLOAD DUP4 MLOAD DUP2 DUP5 MUL DUP2 ADD DUP5 ADD SWAP1 SWAP5 MSTORE DUP1 DUP5 MSTORE PUSH1 0x60 SWAP4 SWAP3 DUP4 ADD DUP3 DUP3 DUP1 ISZERO PUSH2 0xD3B JUMPI PUSH1 0x20 MUL DUP3 ADD SWAP2 SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 JUMPDEST DUP2 SLOAD DUP2 MSTORE PUSH1 0x20 ADD SWAP1 PUSH1 0x1 ADD SWAP1 DUP1 DUP4 GT PUSH2 0xD27 JUMPI JUMPDEST POP POP POP POP POP SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST DUP3 DUP1 SLOAD DUP3 DUP3 SSTORE SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 DUP2 ADD SWAP3 DUP3 ISZERO PUSH2 0xD8D JUMPI SWAP2 PUSH1 0x20 MUL DUP3 ADD JUMPDEST DUP3 DUP2 GT ISZERO PUSH2 0xD8D JUMPI DUP3 MLOAD DUP3 SWAP1 PUSH2 0xD7D SWAP1 DUP3 PUSH2 0x1328 JUMP JUMPDEST POP SWAP2 PUSH1 0x20 ADD SWAP2 SWAP1 PUSH1 0x1 ADD SWAP1 PUSH2 0xD67 JUMP JUMPDEST POP PUSH2 0xD99 SWAP3 SWAP2 POP PUSH2 0xDE4 JUMP JUMPDEST POP SWAP1 JUMP JUMPDEST DUP3 DUP1 SLOAD DUP3 DUP3 SSTORE SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 DUP2 ADD SWAP3 DUP3 ISZERO PUSH2 0xDD8 JUMPI SWAP2 PUSH1 0x20 MUL DUP3 ADD JUMPDEST DUP3 DUP2 GT ISZERO PUSH2 0xDD8 JUMPI DUP3 MLOAD DUP3 SSTORE SWAP2 PUSH1 0x20 ADD SWAP2 SWAP1 PUSH1 0x1 ADD SWAP1 PUSH2 0xDBD JUMP JUMPDEST POP PUSH2 0xD99 SWAP3 SWAP2 POP PUSH2 0xE01 JUMP JUMPDEST DUP1 DUP3 GT ISZERO PUSH2 0xD99 JUMPI PUSH1 0x0 PUSH2 0xDF8 DUP3 DUP3 PUSH2 0xE16 JUMP JUMPDEST POP PUSH1 0x1 ADD PUSH2 0xDE4 JUMP JUMPDEST JUMPDEST DUP1 DUP3 GT ISZERO PUSH2 0xD99 JUMPI PUSH1 0x0 DUP2 SSTORE PUSH1 0x1 ADD PUSH2 0xE02 JUMP JUMPDEST POP DUP1 SLOAD PUSH2 0xE22 SWAP1 PUSH2 0x1278 JUMP JUMPDEST PUSH1 0x0 DUP3 SSTORE DUP1 PUSH1 0x1F LT PUSH2 0xE32 JUMPI POP POP JUMP JUMPDEST PUSH1 0x1F ADD PUSH1 0x20 SWAP1 DIV SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 DUP2 ADD SWAP1 PUSH2 0xE50 SWAP2 SWAP1 PUSH2 0xE01 JUMP JUMPDEST POP JUMP JUMPDEST DUP1 CALLDATALOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP2 AND DUP2 EQ PUSH2 0xE6A JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP1 PUSH1 0x40 DUP4 DUP6 SUB SLT ISZERO PUSH2 0xE82 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP3 CALLDATALOAD SWAP2 POP PUSH2 0xE92 PUSH1 0x20 DUP5 ADD PUSH2 0xE53 JUMP JUMPDEST SWAP1 POP SWAP3 POP SWAP3 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0xEAD JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP CALLDATALOAD SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP2 MLOAD DUP1 DUP5 MSTORE PUSH1 0x0 JUMPDEST DUP2 DUP2 LT ISZERO PUSH2 0xEDA JUMPI PUSH1 0x20 DUP2 DUP6 ADD DUP2 ADD MLOAD DUP7 DUP4 ADD DUP3 ADD MSTORE ADD PUSH2 0xEBE JUMP JUMPDEST POP PUSH1 0x0 PUSH1 0x20 DUP3 DUP7 ADD ADD MSTORE PUSH1 0x20 PUSH1 0x1F NOT PUSH1 0x1F DUP4 ADD AND DUP6 ADD ADD SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 DUP2 MLOAD DUP1 DUP5 MSTORE PUSH1 0x20 DUP1 DUP6 ADD SWAP5 POP DUP1 DUP5 ADD PUSH1 0x0 JUMPDEST DUP4 DUP2 LT ISZERO PUSH2 0xF2A JUMPI DUP2 MLOAD DUP8 MSTORE SWAP6 DUP3 ADD SWAP6 SWAP1 DUP3 ADD SWAP1 PUSH1 0x1 ADD PUSH2 0xF0E JUMP JUMPDEST POP SWAP5 SWAP6 SWAP5 POP POP POP POP POP JUMP JUMPDEST PUSH1 0xC0 DUP2 MSTORE PUSH1 0x0 PUSH2 0xF48 PUSH1 0xC0 DUP4 ADD DUP10 PUSH2 0xEB4 JUMP JUMPDEST PUSH1 0x20 DUP4 DUP3 SUB DUP2 DUP6 ADD MSTORE DUP2 DUP10 MLOAD DUP1 DUP5 MSTORE DUP3 DUP5 ADD SWAP2 POP DUP3 DUP2 PUSH1 0x5 SHL DUP6 ADD ADD DUP4 DUP13 ADD PUSH1 0x0 JUMPDEST DUP4 DUP2 LT ISZERO PUSH2 0xF98 JUMPI PUSH1 0x1F NOT DUP8 DUP5 SUB ADD DUP6 MSTORE PUSH2 0xF86 DUP4 DUP4 MLOAD PUSH2 0xEB4 JUMP JUMPDEST SWAP5 DUP7 ADD SWAP5 SWAP3 POP SWAP1 DUP6 ADD SWAP1 PUSH1 0x1 ADD PUSH2 0xF6A JUMP JUMPDEST POP POP DUP7 DUP2 SUB PUSH1 0x40 DUP9 ADD MSTORE PUSH2 0xFAC DUP2 DUP13 PUSH2 0xEFA JUMP JUMPDEST SWAP6 POP POP POP POP POP POP PUSH2 0xFC1 PUSH1 0x60 DUP4 ADD DUP7 ISZERO ISZERO SWAP1 MSTORE JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP4 SWAP1 SWAP4 AND PUSH1 0x80 DUP3 ADD MSTORE PUSH1 0xA0 ADD MSTORE SWAP5 SWAP4 POP POP POP POP JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x41 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH1 0x1F DUP3 ADD PUSH1 0x1F NOT AND DUP2 ADD PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT DUP3 DUP3 LT OR ISZERO PUSH2 0x101D JUMPI PUSH2 0x101D PUSH2 0xFDE JUMP JUMPDEST PUSH1 0x40 MSTORE SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP3 PUSH1 0x1F DUP4 ADD SLT PUSH2 0x1036 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP2 CALLDATALOAD PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT ISZERO PUSH2 0x1050 JUMPI PUSH2 0x1050 PUSH2 0xFDE JUMP JUMPDEST PUSH2 0x1063 PUSH1 0x1F DUP3 ADD PUSH1 0x1F NOT AND PUSH1 0x20 ADD PUSH2 0xFF4 JUMP JUMPDEST DUP2 DUP2 MSTORE DUP5 PUSH1 0x20 DUP4 DUP7 ADD ADD GT ISZERO PUSH2 0x1078 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP2 PUSH1 0x20 DUP6 ADD PUSH1 0x20 DUP4 ADD CALLDATACOPY PUSH1 0x0 SWAP2 DUP2 ADD PUSH1 0x20 ADD SWAP2 SWAP1 SWAP2 MSTORE SWAP4 SWAP3 POP POP POP JUMP JUMPDEST PUSH1 0x0 DUP1 PUSH1 0x40 DUP4 DUP6 SUB SLT ISZERO PUSH2 0x10A8 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP3 CALLDATALOAD PUSH8 0xFFFFFFFFFFFFFFFF DUP1 DUP3 GT ISZERO PUSH2 0x10C0 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH2 0x10CC DUP7 DUP4 DUP8 ADD PUSH2 0x1025 JUMP JUMPDEST SWAP4 POP PUSH1 0x20 SWAP2 POP DUP2 DUP6 ADD CALLDATALOAD DUP2 DUP2 GT ISZERO PUSH2 0x10E3 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP6 ADD PUSH1 0x1F DUP2 ADD DUP8 SGT PUSH2 0x10F4 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP1 CALLDATALOAD DUP3 DUP2 GT ISZERO PUSH2 0x1106 JUMPI PUSH2 0x1106 PUSH2 0xFDE JUMP JUMPDEST DUP1 PUSH1 0x5 SHL PUSH2 0x1115 DUP6 DUP3 ADD PUSH2 0xFF4 JUMP JUMPDEST SWAP2 DUP3 MSTORE DUP3 DUP2 ADD DUP6 ADD SWAP2 DUP6 DUP2 ADD SWAP1 DUP11 DUP5 GT ISZERO PUSH2 0x112F JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP7 DUP6 ADD SWAP3 POP JUMPDEST DUP4 DUP4 LT ISZERO PUSH2 0x116B JUMPI DUP3 CALLDATALOAD DUP7 DUP2 GT ISZERO PUSH2 0x114D JUMPI PUSH1 0x0 DUP1 DUP2 REVERT JUMPDEST PUSH2 0x115B DUP13 DUP10 DUP4 DUP10 ADD ADD PUSH2 0x1025 JUMP JUMPDEST DUP4 MSTORE POP SWAP2 DUP7 ADD SWAP2 SWAP1 DUP7 ADD SWAP1 PUSH2 0x1135 JUMP JUMPDEST DUP1 SWAP8 POP POP POP POP POP POP POP POP SWAP3 POP SWAP3 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP1 PUSH1 0x40 DUP4 DUP6 SUB SLT ISZERO PUSH2 0x118F JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH2 0x1198 DUP4 PUSH2 0xE53 JUMP JUMPDEST SWAP5 PUSH1 0x20 SWAP4 SWAP1 SWAP4 ADD CALLDATALOAD SWAP4 POP POP POP JUMP JUMPDEST PUSH1 0x80 DUP2 MSTORE PUSH1 0x0 PUSH2 0x11B9 PUSH1 0x80 DUP4 ADD DUP8 PUSH2 0xEB4 JUMP JUMPDEST SWAP5 ISZERO ISZERO PUSH1 0x20 DUP4 ADD MSTORE POP PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP3 SWAP1 SWAP3 AND PUSH1 0x40 DUP4 ADD MSTORE PUSH1 0x60 SWAP1 SWAP2 ADD MSTORE SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP1 PUSH1 0x40 DUP4 DUP6 SUB SLT ISZERO PUSH2 0x11F1 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP POP DUP1 CALLDATALOAD SWAP3 PUSH1 0x20 SWAP1 SWAP2 ADD CALLDATALOAD SWAP2 POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0x1212 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH2 0x121B DUP3 PUSH2 0xE53 JUMP JUMPDEST SWAP4 SWAP3 POP POP POP JUMP JUMPDEST PUSH1 0x20 DUP2 MSTORE PUSH1 0x0 PUSH2 0x121B PUSH1 0x20 DUP4 ADD DUP5 PUSH2 0xEFA JUMP JUMPDEST PUSH1 0x20 DUP1 DUP3 MSTORE PUSH1 0x13 SWAP1 DUP3 ADD MSTORE PUSH19 0x141BDB1B08191BD95CC81B9BDD08195E1A5CDD PUSH1 0x6A SHL PUSH1 0x40 DUP3 ADD MSTORE PUSH1 0x60 ADD SWAP1 JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x32 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH1 0x1 DUP2 DUP2 SHR SWAP1 DUP3 AND DUP1 PUSH2 0x128C JUMPI PUSH1 0x7F DUP3 AND SWAP2 POP JUMPDEST PUSH1 0x20 DUP3 LT DUP2 SUB PUSH2 0x12AC JUMPI PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x22 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x1 DUP3 ADD PUSH2 0x12D2 JUMPI PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST POP PUSH1 0x1 ADD SWAP1 JUMP JUMPDEST PUSH1 0x1F DUP3 GT ISZERO PUSH2 0x1323 JUMPI PUSH1 0x0 DUP2 DUP2 MSTORE PUSH1 0x20 DUP2 KECCAK256 PUSH1 0x1F DUP6 ADD PUSH1 0x5 SHR DUP2 ADD PUSH1 0x20 DUP7 LT ISZERO PUSH2 0x1300 JUMPI POP DUP1 JUMPDEST PUSH1 0x1F DUP6 ADD PUSH1 0x5 SHR DUP3 ADD SWAP2 POP JUMPDEST DUP2 DUP2 LT ISZERO PUSH2 0x131F JUMPI DUP3 DUP2 SSTORE PUSH1 0x1 ADD PUSH2 0x130C JUMP JUMPDEST POP POP POP JUMPDEST POP POP POP JUMP JUMPDEST DUP2 MLOAD PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT ISZERO PUSH2 0x1342 JUMPI PUSH2 0x1342 PUSH2 0xFDE JUMP JUMPDEST PUSH2 0x1356 DUP2 PUSH2 0x1350 DUP5 SLOAD PUSH2 0x1278 JUMP JUMPDEST DUP5 PUSH2 0x12D9 JUMP JUMPDEST PUSH1 0x20 DUP1 PUSH1 0x1F DUP4 GT PUSH1 0x1 DUP2 EQ PUSH2 0x138B JUMPI PUSH1 0x0 DUP5 ISZERO PUSH2 0x1373 JUMPI POP DUP6 DUP4 ADD MLOAD JUMPDEST PUSH1 0x0 NOT PUSH1 0x3 DUP7 SWAP1 SHL SHR NOT AND PUSH1 0x1 DUP6 SWAP1 SHL OR DUP6 SSTORE PUSH2 0x131F JUMP JUMPDEST PUSH1 0x0 DUP6 DUP2 MSTORE PUSH1 0x20 DUP2 KECCAK256 PUSH1 0x1F NOT DUP7 AND SWAP2 JUMPDEST DUP3 DUP2 LT ISZERO PUSH2 0x13BA JUMPI DUP9 DUP7 ADD MLOAD DUP3 SSTORE SWAP5 DUP5 ADD SWAP5 PUSH1 0x1 SWAP1 SWAP2 ADD SWAP1 DUP5 ADD PUSH2 0x139B JUMP JUMPDEST POP DUP6 DUP3 LT ISZERO PUSH2 0x13D8 JUMPI DUP8 DUP6 ADD MLOAD PUSH1 0x0 NOT PUSH1 0x3 DUP9 SWAP1 SHL PUSH1 0xF8 AND SHR NOT AND DUP2 SSTORE JUMPDEST POP POP POP POP POP PUSH1 0x1 SWAP1 DUP2 SHL ADD SWAP1 SSTORE POP JUMP JUMPDEST PUSH1 0x40 DUP2 MSTORE PUSH1 0x0 PUSH2 0x13FB PUSH1 0x40 DUP4 ADD DUP6 PUSH2 0xEB4 JUMP JUMPDEST SWAP1 POP DUP3 PUSH1 0x20 DUP4 ADD MSTORE SWAP4 SWAP3 POP POP POP JUMP INVALID LOG2 PUSH5 0x6970667358 0x22 SLT KECCAK256 CALLDATALOAD 0x29 0x22 JUMPI SWAP14 SELFDESTRUCT BALANCE 0x2E PUSH26 0xF227E53AB804CA115CB3517539EE26DDC29265347D099A64736F PUSH13 0x63430008130033000000000000 ", "sourceMap": "159:6101:0:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5256:255;;;;;;:::i;:::-;;:::i;:::-;;;597:25:1;;;585:2;570:18;5256:255:0;;;;;;;;4619:92;4666:7;4692:12;4619:92;;3964:555;;;;;;:::i;:::-;;:::i;:::-;;;;;;;;;;;;:::i;5629:326::-;;;;;;:::i;:::-;;:::i;:::-;;2194:746;;;;;;:::i;:::-;;:::i;571:49::-;;;;;;:::i;:::-;;:::i;480:24::-;;;;;;455:19;;;;;;:::i;:::-;;:::i;:::-;;;;;;;;;;:::i;3088:488::-;;;;;;:::i;:::-;;:::i;4894:179::-;;;;;;:::i;:::-;;:::i;:::-;;;6704:14:1;;6697:22;6679:41;;6667:2;6652:18;4894:179:0;6539:187:1;6128:130:0;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;5256:255::-;5374:7;1082:12;;5356:7;;1072:22;;1064:54;;;;-1:-1:-1;;;1064:54:0;;;;;;;:::i;:::-;;;;;;;;;5401:5:::1;5407:7;5401:14;;;;;;;;:::i;:::-;;::::0;;;::::1;::::0;;;-1:-1:-1;;;;;5401:30:0;::::1;::::0;;:23:::1;:14;::::0;;::::1;;:23:::0;;;::::1;:30:::0;;;;;;::::1;;5393:61;;;::::0;-1:-1:-1;;;5393:61:0;;7870:2:1;5393:61:0::1;::::0;::::1;7852:21:1::0;7909:2;7889:18;;;7882:30;-1:-1:-1;;;7928:18:1;;;7921:48;7986:18;;5393:61:0::1;7668:342:1::0;5393:61:0::1;5471:5;5477:7;5471:14;;;;;;;;:::i;:::-;;::::0;;;::::1;::::0;;;-1:-1:-1;;;;;5471:33:0;::::1;::::0;;:26:::1;:14;::::0;;::::1;;:26:::0;;;::::1;:33:::0;;;;;;;-1:-1:-1;5256:255:0;;;;;:::o;3964:555::-;4086:22;4122:23;4159:22;4195:13;4222:15;4251:17;4047:7;1082:5;:12;;;;1072:7;:22;1064:54;;;;-1:-1:-1;;;1064:54:0;;;;;;;:::i;:::-;4293:17:::1;4313:5:::0;4319:7:::1;4313:14;;;;;;;;:::i;:::-;;::::0;;;::::1;::::0;;;::::1;::::0;;::::1;;4435:13;::::0;::::1;::::0;4488:14:::1;::::0;::::1;::::0;4337:175;;4313:14;;-1:-1:-1;4313:14:0;;4385:12:::1;::::0;::::1;::::0;4411:10:::1;::::0;::::1;::::0;4435:13:::1;::::0;::::1;::::0;::::1;4462:12:::0;;::::1;-1:-1:-1::0;;;;;4462:12:0::1;::::0;4313:14;;4337:175:::1;::::0;::::1;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3964:555:::0;;;;;;;;:::o;5629:326::-;1082:5;:12;5710:7;;1072:22;;1064:54;;;;-1:-1:-1;;;1064:54:0;;;;;;;:::i;:::-;5768:10:::1;-1:-1:-1::0;;;;;5742:36:0::1;:5;5748:7;5742:14;;;;;;;;:::i;:::-;;::::0;;;::::1;::::0;;;::::1;::::0;;::::1;;:22;;::::0;::::1;::::0;::::1;-1:-1:-1::0;;;;;5742:22:0::1;:36;5734:79;;;::::0;-1:-1:-1;;;5734:79:0;;8602:2:1;5734:79:0::1;::::0;::::1;8584:21:1::0;8641:2;8621:18;;;8614:30;8680:32;8660:18;;;8653:60;8730:18;;5734:79:0::1;8400:354:1::0;5734:79:0::1;5859:5;5865:7;5859:14;;;;;;;;:::i;:::-;;::::0;;;::::1;::::0;;:23:::1;:14;::::0;;::::1;;:23;::::0;5832:14;;5859:23:::1;::::0;;::::1;5858:24;::::0;5859:14;5838:7;;5832:14;::::1;;;;;:::i;:::-;;;;;;;;;;;:23;;;:50;;;;;;;;;;;;;;;;;;5915:7;5897:51;5924:5;5930:7;5924:14;;;;;;;;:::i;:::-;;::::0;;;::::1;::::0;;;:23:::1;:14;::::0;;::::1;;:23;::::0;5897:51:::1;::::0;::::1;::::0;5924:23:::1;;6704:14:1::0;6697:22;6679:41;;6667:2;6652:18;;6539:187;5897:51:0::1;;;;;;;;5629:326:::0;;:::o;2194:746::-;2340:14;2310:9;2321:8;1684:1;1664:9;1658:23;:27;1650:64;;;;-1:-1:-1;;;1650:64:0;;8961:2:1;1650:64:0;;;8943:21:1;9000:2;8980:18;;;8973:30;9039:26;9019:18;;;9012:54;9083:18;;1650:64:0;8759:348:1;1650:64:0;1751:1;1732:8;:15;:20;;1724:60;;;;-1:-1:-1;;;1724:60:0;;9314:2:1;1724:60:0;;;9296:21:1;9353:2;9333:18;;;9326:30;9392:29;9372:18;;;9365:57;9439:18;;1724:60:0;9112:351:1;1724:60:0;1821:2;1802:8;:15;:21;;1794:50;;;;-1:-1:-1;;;1794:50:0;;9670:2:1;1794:50:0;;;9652:21:1;9709:2;9689:18;;;9682:30;-1:-1:-1;;;9728:18:1;;;9721:46;9784:18;;1794:50:0;9468:340:1;1794:50:0;1860:6;1855:132;1876:8;:15;1872:1;:19;1855:132;;;1948:1;1926:8;1935:1;1926:11;;;;;;;;:::i;:::-;;;;;;;1920:25;:29;1912:64;;;;-1:-1:-1;;;1912:64:0;;10015:2:1;1912:64:0;;;9997:21:1;10054:2;10034:18;;;10027:30;-1:-1:-1;;;10073:18:1;;;10066:52;10135:18;;1912:64:0;9813:346:1;1912:64:0;1893:3;;;;:::i;:::-;;;;1855:132;;;-1:-1:-1;2375:5:0::1;:12:::0;;2433::::1;::::0;::::1;::::0;;;;;;2375;;-1:-1:-1;2375:5:0;;:12;;2478:13;::::1;;;;;:::i;:::-;;::::0;;;::::1;::::0;;;::::1;::::0;;::::1;;::::0;-1:-1:-1;2478:13:0;2510:28:::1;2529:9:::0;2478:13;2510:28:::1;:::i;:::-;-1:-1:-1::0;2548:26:0;;::::1;::::0;:15:::1;::::0;::::1;::::0;:26:::1;::::0;::::1;::::0;::::1;:::i;:::-;;2614:8;:15;2600:30;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;-1:-1:-1;2600:30:0::1;-1:-1:-1::0;2584:46:0;;::::1;::::0;:13:::1;::::0;::::1;::::0;:46:::1;::::0;;::::1;::::0;::::1;:::i;:::-;-1:-1:-1::0;2640:16:0::1;::::0;::::1;:23:::0;;2659:4:::1;-1:-1:-1::0;;;;;;2673:28:0;;;2640:23:::1;2691:10;2673:28;::::0;;;;;;2731:15:::1;2711:17;::::0;::::1;:35:::0;2792:11;;;-1:-1:-1;2792:11:0::1;::::0;::::1;:::i;:::-;::::0;;;-1:-1:-1;;2826:10:0::1;2813:24;::::0;;;:12:::1;:24;::::0;;;;;;;:37;;::::1;::::0;::::1;::::0;;;;;;;;;::::1;::::0;;;2917:15;;2874:59;;2843:6;;2874:59:::1;::::0;::::1;::::0;2906:9;;2874:59:::1;:::i;:::-;;;;;;;;2356:584;2194:746:::0;;;;;;:::o;571:49::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;455:19::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;455:19:0;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;455:19:0;;-1:-1:-1;455:19:0;:::o;3088:488::-;1082:5;:12;3201:7;;1072:22;;1064:54;;;;-1:-1:-1;;;1064:54:0;;;;;;;:::i;:::-;3230:7:::1;1197:5;1203:7;1197:14;;;;;;;;:::i;:::-;;::::0;;;::::1;::::0;;;:23:::1;:14;::::0;;::::1;;:23;::::0;::::1;;1189:54;;;::::0;-1:-1:-1;;;1189:54:0;;13103:2:1;1189:54:0::1;::::0;::::1;13085:21:1::0;13142:2;13122:18;;;13115:30;-1:-1:-1;;;13161:18:1;;;13154:48;13219:18;;1189:54:0::1;12901:342:1::0;1189:54:0::1;3260:7:::2;1324:5;1330:7;1324:14;;;;;;;;:::i;:::-;;::::0;;;::::2;::::0;;;1348:10:::2;1324:35:::0;;:23:::2;:14;::::0;;::::2;;:23:::0;;;::::2;:35:::0;;;;;;::::2;;1323:36;1315:62;;;::::0;-1:-1:-1;;;1315:62:0;;13450:2:1;1315:62:0::2;::::0;::::2;13432:21:1::0;13489:2;13469:18;;;13462:30;-1:-1:-1;;;13508:18:1;;;13501:43;13561:18;;1315:62:0::2;13248:337:1::0;1315:62:0::2;3290:7:::3;3299:12;1494:5;1500:7;1494:14;;;;;;;;:::i;:::-;;;;;;;;;;;:22;;:29;;;;1479:12;:44;1471:71;;;::::0;-1:-1:-1;;;1471:71:0;;13792:2:1;1471:71:0::3;::::0;::::3;13774:21:1::0;13831:2;13811:18;;;13804:30;-1:-1:-1;;;13850:18:1;;;13843:44;13904:18;;1471:71:0::3;13590:338:1::0;1471:71:0::3;3328:17:::4;3348:5:::0;3354:7:::4;3348:14;;;;;;;;:::i;:::-;;::::0;;;::::4;::::0;;;3395:10:::4;3381:25:::0;;:13:::4;3348:14;::::0;;::::4;;3381:13:::0;;::::4;:25:::0;;;;;;:32;;-1:-1:-1;;3381:32:0::4;3409:4;3381:32;::::0;;3423:16:::4;::::0;::::4;:28:::0;;;;;;:43;;;3476:10:::4;::::0;::::4;:24:::0;;3348:14;;-1:-1:-1;3476:10:0;3454:12;;3476:24;::::4;;;;;:::i;:::-;;::::0;;;::::4;::::0;;::::4;:26:::0;;;::::4;::::0;::::4;:::i;:::-;::::0;;;-1:-1:-1;;3526:43:0::4;::::0;597:25:1;;;3544:10:0::4;::::0;3535:7;;3526:43:::4;::::0;585:2:1;570:18;3526:43:0::4;;;;;;;3318:258;1387:1:::3;;1253::::2;1128::::1;3088:488:::0;;;:::o;4894:179::-;5013:4;1082:12;;4995:7;;1072:22;;1064:54;;;;-1:-1:-1;;;1064:54:0;;;;;;;:::i;:::-;5036:5:::1;5042:7;5036:14;;;;;;;;:::i;:::-;;::::0;;;::::1;::::0;;;-1:-1:-1;;;;;5036:30:0;::::1;::::0;;:23:::1;:14;::::0;;::::1;;:23:::0;;;::::1;:30:::0;;;;;;::::1;;::::0;-1:-1:-1;4894:179:0;;;;;:::o;6128:130::-;-1:-1:-1;;;;;6229:22:0;;;;;;:12;:22;;;;;;;;;6222:29;;;;;;;;;;;;;;;;;6194:16;;6222:29;;;6229:22;6222:29;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6128:130;;;:::o;-1:-1:-1:-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;:::i;:::-;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;:::i;:::-;;:::o;14:173:1:-;82:20;;-1:-1:-1;;;;;131:31:1;;121:42;;111:70;;177:1;174;167:12;111:70;14:173;;;:::o;192:254::-;260:6;268;321:2;309:9;300:7;296:23;292:32;289:52;;;337:1;334;327:12;289:52;373:9;360:23;350:33;;402:38;436:2;425:9;421:18;402:38;:::i;:::-;392:48;;192:254;;;;;:::o;633:180::-;692:6;745:2;733:9;724:7;720:23;716:32;713:52;;;761:1;758;751:12;713:52;-1:-1:-1;784:23:1;;633:180;-1:-1:-1;633:180:1:o;818:423::-;860:3;898:5;892:12;925:6;920:3;913:19;950:1;960:162;974:6;971:1;968:13;960:162;;;1036:4;1092:13;;;1088:22;;1082:29;1064:11;;;1060:20;;1053:59;989:12;960:162;;;964:3;1167:1;1160:4;1151:6;1146:3;1142:16;1138:27;1131:38;1230:4;1223:2;1219:7;1214:2;1206:6;1202:15;1198:29;1193:3;1189:39;1185:50;1178:57;;;818:423;;;;:::o;1246:435::-;1299:3;1337:5;1331:12;1364:6;1359:3;1352:19;1390:4;1419:2;1414:3;1410:12;1403:19;;1456:2;1449:5;1445:14;1477:1;1487:169;1501:6;1498:1;1495:13;1487:169;;;1562:13;;1550:26;;1596:12;;;;1631:15;;;;1523:1;1516:9;1487:169;;;-1:-1:-1;1672:3:1;;1246:435;-1:-1:-1;;;;;1246:435:1:o;1891:1335::-;2294:3;2283:9;2276:22;2257:4;2321:46;2362:3;2351:9;2347:19;2339:6;2321:46;:::i;:::-;2386:2;2436:9;2428:6;2424:22;2419:2;2408:9;2404:18;2397:50;2467:6;2502;2496:13;2533:6;2525;2518:22;2568:2;2560:6;2556:15;2549:22;;2627:2;2617:6;2614:1;2610:14;2602:6;2598:27;2594:36;2665:2;2657:6;2653:15;2686:1;2696:252;2710:6;2707:1;2704:13;2696:252;;;2800:2;2796:7;2787:6;2779;2775:19;2771:33;2766:3;2759:46;2828:40;2861:6;2852;2846:13;2828:40;:::i;:::-;2926:12;;;;2818:50;-1:-1:-1;2891:15:1;;;;2732:1;2725:9;2696:252;;;2700:3;;2996:9;2988:6;2984:22;2979:2;2968:9;2964:18;2957:50;3024:44;3061:6;3053;3024:44;:::i;:::-;3016:52;;;;;;;3077:43;3116:2;3105:9;3101:18;3093:6;1756:13;1749:21;1737:34;;1686:91;3077:43;-1:-1:-1;;;;;1848:31:1;;;;3171:3;3156:19;;1836:44;3207:3;3192:19;3185:35;1891:1335;;-1:-1:-1;;;;1891:1335:1:o;3231:127::-;3292:10;3287:3;3283:20;3280:1;3273:31;3323:4;3320:1;3313:15;3347:4;3344:1;3337:15;3363:275;3434:2;3428:9;3499:2;3480:13;;-1:-1:-1;;3476:27:1;3464:40;;3534:18;3519:34;;3555:22;;;3516:62;3513:88;;;3581:18;;:::i;:::-;3617:2;3610:22;3363:275;;-1:-1:-1;3363:275:1:o;3643:531::-;3686:5;3739:3;3732:4;3724:6;3720:17;3716:27;3706:55;;3757:1;3754;3747:12;3706:55;3793:6;3780:20;3819:18;3815:2;3812:26;3809:52;;;3841:18;;:::i;:::-;3885:55;3928:2;3909:13;;-1:-1:-1;;3905:27:1;3934:4;3901:38;3885:55;:::i;:::-;3965:2;3956:7;3949:19;4011:3;4004:4;3999:2;3991:6;3987:15;3983:26;3980:35;3977:55;;;4028:1;4025;4018:12;3977:55;4093:2;4086:4;4078:6;4074:17;4067:4;4058:7;4054:18;4041:55;4141:1;4116:16;;;4134:4;4112:27;4105:38;;;;4120:7;3643:531;-1:-1:-1;;;3643:531:1:o;4179:1367::-;4292:6;4300;4353:2;4341:9;4332:7;4328:23;4324:32;4321:52;;;4369:1;4366;4359:12;4321:52;4409:9;4396:23;4438:18;4479:2;4471:6;4468:14;4465:34;;;4495:1;4492;4485:12;4465:34;4518:50;4560:7;4551:6;4540:9;4536:22;4518:50;:::i;:::-;4508:60;;4587:2;4577:12;;4642:2;4631:9;4627:18;4614:32;4671:2;4661:8;4658:16;4655:36;;;4687:1;4684;4677:12;4655:36;4710:24;;4765:4;4757:13;;4753:27;-1:-1:-1;4743:55:1;;4794:1;4791;4784:12;4743:55;4830:2;4817:16;4852:2;4848;4845:10;4842:36;;;4858:18;;:::i;:::-;4904:2;4901:1;4897:10;4927:28;4951:2;4947;4943:11;4927:28;:::i;:::-;4989:15;;;5059:11;;;5055:20;;;5020:12;;;;5087:19;;;5084:39;;;5119:1;5116;5109:12;5084:39;5151:2;5147;5143:11;5132:22;;5163:353;5179:6;5174:3;5171:15;5163:353;;;5265:3;5252:17;5301:2;5288:11;5285:19;5282:109;;;5345:1;5374:2;5370;5363:14;5282:109;5416:57;5465:7;5460:2;5446:11;5442:2;5438:20;5434:29;5416:57;:::i;:::-;5404:70;;-1:-1:-1;5196:12:1;;;;5494;;;;5163:353;;;5535:5;5525:15;;;;;;;;;4179:1367;;;;;:::o;5551:254::-;5619:6;5627;5680:2;5668:9;5659:7;5655:23;5651:32;5648:52;;;5696:1;5693;5686:12;5648:52;5719:29;5738:9;5719:29;:::i;:::-;5709:39;5795:2;5780:18;;;;5767:32;;-1:-1:-1;;;5551:254:1:o;5810:471::-;6037:3;6026:9;6019:22;6000:4;6058:46;6099:3;6088:9;6084:19;6076:6;6058:46;:::i;:::-;6147:14;;6140:22;6135:2;6120:18;;6113:50;-1:-1:-1;;;;;;6199:32:1;;;;6194:2;6179:18;;6172:60;6263:2;6248:18;;;6241:34;6050:54;5810:471;-1:-1:-1;5810:471:1:o;6286:248::-;6354:6;6362;6415:2;6403:9;6394:7;6390:23;6386:32;6383:52;;;6431:1;6428;6421:12;6383:52;-1:-1:-1;;6454:23:1;;;6524:2;6509:18;;;6496:32;;-1:-1:-1;6286:248:1:o;6731:186::-;6790:6;6843:2;6831:9;6822:7;6818:23;6814:32;6811:52;;;6859:1;6856;6849:12;6811:52;6882:29;6901:9;6882:29;:::i;:::-;6872:39;6731:186;-1:-1:-1;;;6731:186:1:o;6922:261::-;7101:2;7090:9;7083:21;7064:4;7121:56;7173:2;7162:9;7158:18;7150:6;7121:56;:::i;7188:343::-;7390:2;7372:21;;;7429:2;7409:18;;;7402:30;-1:-1:-1;;;7463:2:1;7448:18;;7441:49;7522:2;7507:18;;7188:343::o;7536:127::-;7597:10;7592:3;7588:20;7585:1;7578:31;7628:4;7625:1;7618:15;7652:4;7649:1;7642:15;8015:380;8094:1;8090:12;;;;8137;;;8158:61;;8212:4;8204:6;8200:17;8190:27;;8158:61;8265:2;8257:6;8254:14;8234:18;8231:38;8228:161;;8311:10;8306:3;8302:20;8299:1;8292:31;8346:4;8343:1;8336:15;8374:4;8371:1;8364:15;8228:161;;8015:380;;;:::o;10164:232::-;10203:3;10224:17;;;10221:140;;10283:10;10278:3;10274:20;10271:1;10264:31;10318:4;10315:1;10308:15;10346:4;10343:1;10336:15;10221:140;-1:-1:-1;10388:1:1;10377:13;;10164:232::o;10527:545::-;10629:2;10624:3;10621:11;10618:448;;;10665:1;10690:5;10686:2;10679:17;10735:4;10731:2;10721:19;10805:2;10793:10;10789:19;10786:1;10782:27;10776:4;10772:38;10841:4;10829:10;10826:20;10823:47;;;-1:-1:-1;10864:4:1;10823:47;10919:2;10914:3;10910:12;10907:1;10903:20;10897:4;10893:31;10883:41;;10974:82;10992:2;10985:5;10982:13;10974:82;;;11037:17;;;11018:1;11007:13;10974:82;;;10978:3;;;10618:448;10527:545;;;:::o;11248:1352::-;11374:3;11368:10;11401:18;11393:6;11390:30;11387:56;;;11423:18;;:::i;:::-;11452:97;11542:6;11502:38;11534:4;11528:11;11502:38;:::i;:::-;11496:4;11452:97;:::i;:::-;11604:4;;11668:2;11657:14;;11685:1;11680:663;;;;12387:1;12404:6;12401:89;;;-1:-1:-1;12456:19:1;;;12450:26;12401:89;-1:-1:-1;;11205:1:1;11201:11;;;11197:24;11193:29;11183:40;11229:1;11225:11;;;11180:57;12503:81;;11650:944;;11680:663;10474:1;10467:14;;;10511:4;10498:18;;-1:-1:-1;;11716:20:1;;;11834:236;11848:7;11845:1;11842:14;11834:236;;;11937:19;;;11931:26;11916:42;;12029:27;;;;11997:1;11985:14;;;;11864:19;;11834:236;;;11838:3;12098:6;12089:7;12086:19;12083:201;;;12159:19;;;12153:26;-1:-1:-1;;12242:1:1;12238:14;;;12254:3;12234:24;12230:37;12226:42;12211:58;12196:74;;12083:201;-1:-1:-1;;;;;12330:1:1;12314:14;;;12310:22;12297:36;;-1:-1:-1;11248:1352:1:o;12605:291::-;12782:2;12771:9;12764:21;12745:4;12802:45;12843:2;12832:9;12828:18;12820:6;12802:45;:::i;:::-;12794:53;;12883:6;12878:2;12867:9;12863:18;12856:34;12605:291;;;;;:::o"}, "methodIdentifiers": {"createPoll(string,string[])": "7e3d71c3", "creatorPolls(address,uint256)": "8439e976", "getCreatorPolls(address)": "ec81a17a", "getPoll(uint256)": "1a8cbcaa", "getPollCount()": "11a52746", "getUserVote(uint256,address)": "03c7881a", "hasUserVoted(uint256,address)": "dc296ae1", "pollCount()": "9207891d", "polls(uint256)": "ac2f0074", "togglePollStatus(uint256)": "251e02d7", "vote(uint256,uint256)": "b384abef"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.19+commit.7dd6d404\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"pollId\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"creator\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"question\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"optionCount\",\"type\":\"uint256\"}],\"name\":\"PollCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"pollId\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"isActive\",\"type\":\"bool\"}],\"name\":\"PollStatusChanged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"pollId\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"voter\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"optionIndex\",\"type\":\"uint256\"}],\"name\":\"VoteCast\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"_question\",\"type\":\"string\"},{\"internalType\":\"string[]\",\"name\":\"_options\",\"type\":\"string[]\"}],\"name\":\"createPoll\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"pollId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"creatorPolls\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_creator\",\"type\":\"address\"}],\"name\":\"getCreatorPolls\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_pollId\",\"type\":\"uint256\"}],\"name\":\"getPoll\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"question\",\"type\":\"string\"},{\"internalType\":\"string[]\",\"name\":\"options\",\"type\":\"string[]\"},{\"internalType\":\"uint256[]\",\"name\":\"votes\",\"type\":\"uint256[]\"},{\"internalType\":\"bool\",\"name\":\"isActive\",\"type\":\"bool\"},{\"internalType\":\"address\",\"name\":\"creator\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"createdAt\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getPollCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_pollId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_user\",\"type\":\"address\"}],\"name\":\"getUserVote\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_pollId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_user\",\"type\":\"address\"}],\"name\":\"hasUserVoted\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pollCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"polls\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"question\",\"type\":\"string\"},{\"internalType\":\"bool\",\"name\":\"isActive\",\"type\":\"bool\"},{\"internalType\":\"address\",\"name\":\"creator\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"createdAt\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_pollId\",\"type\":\"uint256\"}],\"name\":\"togglePollStatus\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_pollId\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_optionIndex\",\"type\":\"uint256\"}],\"name\":\"vote\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"A simple voting contract for creating polls and casting votes\",\"kind\":\"dev\",\"methods\":{\"createPoll(string,string[])\":{\"details\":\"Create a new poll\",\"params\":{\"_options\":\"Array of voting options\",\"_question\":\"The poll question\"},\"returns\":{\"pollId\":\"The ID of the created poll\"}},\"getCreatorPolls(address)\":{\"details\":\"Get polls created by a specific address\",\"params\":{\"_creator\":\"The creator address\"},\"returns\":{\"_0\":\"Array of poll IDs created by the address\"}},\"getPoll(uint256)\":{\"details\":\"Get poll information\",\"params\":{\"_pollId\":\"The poll ID\"},\"returns\":{\"createdAt\":\"The poll creation timestamp\",\"creator\":\"The poll creator address\",\"isActive\":\"Whether the poll is active\",\"options\":\"Array of voting options\",\"question\":\"The poll question\",\"votes\":\"Array of vote counts for each option\"}},\"getPollCount()\":{\"details\":\"Get the total number of polls\",\"returns\":{\"_0\":\"The total poll count\"}},\"getUserVote(uint256,address)\":{\"details\":\"Get a user's vote choice\",\"params\":{\"_pollId\":\"The poll ID\",\"_user\":\"The user address\"},\"returns\":{\"_0\":\"The user's vote choice (option index)\"}},\"hasUserVoted(uint256,address)\":{\"details\":\"Check if a user has voted on a poll\",\"params\":{\"_pollId\":\"The poll ID\",\"_user\":\"The user address\"},\"returns\":{\"_0\":\"Whether the user has voted\"}},\"togglePollStatus(uint256)\":{\"details\":\"Toggle poll active status (only creator can call)\",\"params\":{\"_pollId\":\"The poll ID\"}},\"vote(uint256,uint256)\":{\"details\":\"Cast a vote on a poll\",\"params\":{\"_optionIndex\":\"The index of the chosen option\",\"_pollId\":\"The poll ID\"}}},\"title\":\"SimpleVoting\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/SimpleVoting.sol\":\"SimpleVoting\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"contracts/SimpleVoting.sol\":{\"keccak256\":\"0xda02224771943091f7855340396603a23e6ce05c7ba9824a334161d74168b4ff\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d6a2685fc71f084dfa58cf52485367f824f72e7a09f35d7ecb94072e910149e8\",\"dweb:/ipfs/QmRWGBh3YefvJngZdikKtFDKPCUBDtAtiZwGhfv8Jb4giA\"]}},\"version\":1}"}}}}}