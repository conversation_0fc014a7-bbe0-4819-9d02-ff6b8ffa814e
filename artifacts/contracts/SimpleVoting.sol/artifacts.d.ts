// This file was autogenerated by hardhat-viem, do not edit it.
// prettier-ignore
// tslint:disable
// eslint-disable

import "hardhat/types/artifacts";
import type { GetContractReturnType } from "@nomicfoundation/hardhat-viem/types";

import { SimpleVoting$Type } from "./SimpleVoting";

declare module "hardhat/types/artifacts" {
  interface ArtifactsMap {
    ["SimpleVoting"]: SimpleVoting$Type;
    ["contracts/SimpleVoting.sol:SimpleVoting"]: SimpleVoting$Type;
  }

  interface ContractTypesMap {
    ["SimpleVoting"]: GetContractReturnType<SimpleVoting$Type["abi"]>;
    ["contracts/SimpleVoting.sol:SimpleVoting"]: GetContractReturnType<SimpleVoting$Type["abi"]>;
  }
}
