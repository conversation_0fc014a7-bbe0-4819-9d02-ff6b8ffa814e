// This file was autogenerated by hardhat-viem, do not edit it.
// prettier-ignore
// tslint:disable
// eslint-disable

import type { Address } from "viem";
import type { GetContractReturnType } from "@nomicfoundation/hardhat-viem/types";
import "@nomicfoundation/hardhat-viem/types";

export interface SimpleVoting$Type {
  "_format": "hh-sol-artifact-1",
  "contractName": "SimpleVoting",
  "sourceName": "contracts/SimpleVoting.sol",
  "abi": [
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "internalType": "uint256",
          "name": "pollId",
          "type": "uint256"
        },
        {
          "indexed": true,
          "internalType": "address",
          "name": "creator",
          "type": "address"
        },
        {
          "indexed": false,
          "internalType": "string",
          "name": "question",
          "type": "string"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "optionCount",
          "type": "uint256"
        }
      ],
      "name": "PollCreated",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "internalType": "uint256",
          "name": "pollId",
          "type": "uint256"
        },
        {
          "indexed": false,
          "internalType": "bool",
          "name": "isActive",
          "type": "bool"
        }
      ],
      "name": "PollStatusChanged",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "internalType": "uint256",
          "name": "pollId",
          "type": "uint256"
        },
        {
          "indexed": true,
          "internalType": "address",
          "name": "voter",
          "type": "address"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "optionIndex",
          "type": "uint256"
        }
      ],
      "name": "VoteCast",
      "type": "event"
    },
    {
      "inputs": [
        {
          "internalType": "string",
          "name": "_question",
          "type": "string"
        },
        {
          "internalType": "string[]",
          "name": "_options",
          "type": "string[]"
        }
      ],
      "name": "createPoll",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "pollId",
          "type": "uint256"
        }
      ],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "name": "creatorPolls",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "_creator",
          "type": "address"
        }
      ],
      "name": "getCreatorPolls",
      "outputs": [
        {
          "internalType": "uint256[]",
          "name": "",
          "type": "uint256[]"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "uint256",
          "name": "_pollId",
          "type": "uint256"
        }
      ],
      "name": "getPoll",
      "outputs": [
        {
          "internalType": "string",
          "name": "question",
          "type": "string"
        },
        {
          "internalType": "string[]",
          "name": "options",
          "type": "string[]"
        },
        {
          "internalType": "uint256[]",
          "name": "votes",
          "type": "uint256[]"
        },
        {
          "internalType": "bool",
          "name": "isActive",
          "type": "bool"
        },
        {
          "internalType": "address",
          "name": "creator",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "createdAt",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "getPollCount",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "uint256",
          "name": "_pollId",
          "type": "uint256"
        },
        {
          "internalType": "address",
          "name": "_user",
          "type": "address"
        }
      ],
      "name": "getUserVote",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "uint256",
          "name": "_pollId",
          "type": "uint256"
        },
        {
          "internalType": "address",
          "name": "_user",
          "type": "address"
        }
      ],
      "name": "hasUserVoted",
      "outputs": [
        {
          "internalType": "bool",
          "name": "",
          "type": "bool"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "pollCount",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "name": "polls",
      "outputs": [
        {
          "internalType": "string",
          "name": "question",
          "type": "string"
        },
        {
          "internalType": "bool",
          "name": "isActive",
          "type": "bool"
        },
        {
          "internalType": "address",
          "name": "creator",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "createdAt",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "uint256",
          "name": "_pollId",
          "type": "uint256"
        }
      ],
      "name": "togglePollStatus",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "uint256",
          "name": "_pollId",
          "type": "uint256"
        },
        {
          "internalType": "uint256",
          "name": "_optionIndex",
          "type": "uint256"
        }
      ],
      "name": "vote",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    }
  ],
  "bytecode": "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",
  "deployedBytecode": "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",
  "linkReferences": {},
  "deployedLinkReferences": {}
}

declare module "@nomicfoundation/hardhat-viem/types" {
  export function deployContract(
    contractName: "SimpleVoting",
    constructorArgs?: [],
    config?: DeployContractConfig
  ): Promise<GetContractReturnType<SimpleVoting$Type["abi"]>>;
  export function deployContract(
    contractName: "contracts/SimpleVoting.sol:SimpleVoting",
    constructorArgs?: [],
    config?: DeployContractConfig
  ): Promise<GetContractReturnType<SimpleVoting$Type["abi"]>>;

  export function sendDeploymentTransaction(
    contractName: "SimpleVoting",
    constructorArgs?: [],
    config?: SendDeploymentTransactionConfig
  ): Promise<{
    contract: GetContractReturnType<SimpleVoting$Type["abi"]>;
    deploymentTransaction: GetTransactionReturnType;
  }>;
  export function sendDeploymentTransaction(
    contractName: "contracts/SimpleVoting.sol:SimpleVoting",
    constructorArgs?: [],
    config?: SendDeploymentTransactionConfig
  ): Promise<{
    contract: GetContractReturnType<SimpleVoting$Type["abi"]>;
    deploymentTransaction: GetTransactionReturnType;
  }>;

  export function getContractAt(
    contractName: "SimpleVoting",
    address: Address,
    config?: GetContractAtConfig
  ): Promise<GetContractReturnType<SimpleVoting$Type["abi"]>>;
  export function getContractAt(
    contractName: "contracts/SimpleVoting.sol:SimpleVoting",
    address: Address,
    config?: GetContractAtConfig
  ): Promise<GetContractReturnType<SimpleVoting$Type["abi"]>>;
}
