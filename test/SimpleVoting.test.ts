import { expect } from "chai";
import hre from "hardhat";
import { getAddress } from "viem";

describe("SimpleVoting", function () {
  let simpleVoting: any;
  let owner: any;
  let addr1: any;
  let addr2: any;

  beforeEach(async function () {
    // Get signers
    [owner, addr1, addr2] = await hre.viem.getWalletClients();

    // Deploy contract
    simpleVoting = await hre.viem.deployContract("SimpleVoting");
  });

  describe("Poll Creation", function () {
    it("Should create a poll with valid data", async function () {
      const question = "What's your favorite color?";
      const options = ["Red", "Blue", "Green"];

      const hash = await simpleVoting.write.createPoll([question, options]);

      // Wait for transaction to be mined
      const publicClient = await hre.viem.getPublicClient();
      await publicClient.waitForTransactionReceipt({ hash });

      const pollCount = await simpleVoting.read.getPollCount();
      expect(pollCount).to.equal(1n);

      const poll = await simpleVoting.read.getPoll([0n]);
      expect(poll[0]).to.equal(question); // question
      expect(poll[1]).to.deep.equal(options); // options
      expect(poll[3]).to.be.true; // isActive
    });

    it("Should reject empty question", async function () {
      await expect(
        simpleVoting.write.createPoll(["", ["Option1", "Option2"]])
      ).to.be.rejectedWith("Question cannot be empty");
    });

    it("Should reject insufficient options", async function () {
      await expect(
        simpleVoting.write.createPoll(["Question?", ["Option1"]])
      ).to.be.rejectedWith("At least 2 options required");
    });

    it("Should reject too many options", async function () {
      const manyOptions = Array.from(
        { length: 11 },
        (_, i) => `Option${i + 1}`
      );
      await expect(
        simpleVoting.write.createPoll(["Question?", manyOptions])
      ).to.be.rejectedWith("Too many options");
    });

    it("Should reject empty options", async function () {
      await expect(
        simpleVoting.write.createPoll(["Question?", ["Option1", ""]])
      ).to.be.rejectedWith("Option cannot be empty");
    });
  });

  describe("Voting", function () {
    beforeEach(async function () {
      await simpleVoting.write.createPoll([
        "Test Poll",
        ["Option1", "Option2"],
      ]);
    });

    it("Should allow valid vote", async function () {
      const hash = await simpleVoting.write.vote([0n, 0n], {
        account: addr1.account,
      });

      const publicClient = await hre.viem.getPublicClient();
      await publicClient.waitForTransactionReceipt({ hash });

      const hasVoted = await simpleVoting.read.hasUserVoted([
        0n,
        addr1.account.address,
      ]);
      expect(hasVoted).to.be.true;

      const userVote = await simpleVoting.read.getUserVote([
        0n,
        addr1.account.address,
      ]);
      expect(userVote).to.equal(0n);
    });

    it("Should prevent double voting", async function () {
      await simpleVoting.write.vote([0n, 0n], {
        account: addr1.account,
      });

      await expect(
        simpleVoting.write.vote([0n, 1n], {
          account: addr1.account,
        })
      ).to.be.rejectedWith("Already voted");
    });

    it("Should reject invalid option", async function () {
      await expect(
        simpleVoting.write.vote([0n, 5n], {
          account: addr1.account,
        })
      ).to.be.rejectedWith("Invalid option");
    });

    it("Should reject voting on non-existent poll", async function () {
      await expect(
        simpleVoting.write.vote([999n, 0n], {
          account: addr1.account,
        })
      ).to.be.rejectedWith("Poll does not exist");
    });

    it("Should update vote counts correctly", async function () {
      await simpleVoting.write.vote([0n, 0n], {
        account: addr1.account,
      });
      await simpleVoting.write.vote([0n, 1n], {
        account: addr2.account,
      });

      const poll = await simpleVoting.read.getPoll([0n]);
      expect(poll[2][0]).to.equal(1n); // votes[0]
      expect(poll[2][1]).to.equal(1n); // votes[1]
    });
  });

  describe("Data Retrieval", function () {
    it("Should return correct poll data", async function () {
      const question = "Test Question";
      const options = ["A", "B", "C"];

      await simpleVoting.write.createPoll([question, options]);
      const poll = await simpleVoting.read.getPoll([0n]);

      expect(poll[0]).to.equal(question); // question
      expect(poll[1]).to.deep.equal(options); // options
      expect(poll[3]).to.be.true; // isActive
      expect(poll[4]).to.equal(getAddress(owner.account.address)); // creator
      expect(poll[2].length).to.equal(options.length); // votes array length
    });

    it("Should return correct poll count", async function () {
      expect(await simpleVoting.read.getPollCount()).to.equal(0n);

      await simpleVoting.write.createPoll(["Poll 1", ["A", "B"]]);
      expect(await simpleVoting.read.getPollCount()).to.equal(1n);

      await simpleVoting.write.createPoll(["Poll 2", ["X", "Y"]]);
      expect(await simpleVoting.read.getPollCount()).to.equal(2n);
    });

    it("Should track creator polls correctly", async function () {
      await simpleVoting.write.createPoll(["Poll 1", ["A", "B"]], {
        account: owner.account,
      });
      await simpleVoting.write.createPoll(["Poll 2", ["X", "Y"]], {
        account: addr1.account,
      });
      await simpleVoting.write.createPoll(["Poll 3", ["M", "N"]], {
        account: owner.account,
      });

      const ownerPolls = await simpleVoting.read.getCreatorPolls([
        owner.account.address,
      ]);
      const addr1Polls = await simpleVoting.read.getCreatorPolls([
        addr1.account.address,
      ]);

      expect(ownerPolls.length).to.equal(2);
      expect(ownerPolls[0]).to.equal(0n);
      expect(ownerPolls[1]).to.equal(2n);

      expect(addr1Polls.length).to.equal(1);
      expect(addr1Polls[0]).to.equal(1n);
    });
  });

  describe("Poll Status Management", function () {
    beforeEach(async function () {
      await simpleVoting.write.createPoll([
        "Test Poll",
        ["Option1", "Option2"],
      ]);
    });

    it("Should allow creator to toggle poll status", async function () {
      await simpleVoting.write.togglePollStatus([0n]);

      const poll = await simpleVoting.read.getPoll([0n]);
      expect(poll[3]).to.be.false; // isActive
    });

    it("Should prevent non-creator from toggling status", async function () {
      await expect(
        simpleVoting.write.togglePollStatus([0n], {
          account: addr1.account,
        })
      ).to.be.rejectedWith("Only creator can toggle status");
    });

    it("Should prevent voting on inactive poll", async function () {
      await simpleVoting.write.togglePollStatus([0n]);

      await expect(
        simpleVoting.write.vote([0n, 0n], {
          account: addr1.account,
        })
      ).to.be.rejectedWith("Poll is not active");
    });
  });

  describe("Edge Cases", function () {
    it("Should handle getUserVote for non-voter", async function () {
      await simpleVoting.write.createPoll([
        "Test Poll",
        ["Option1", "Option2"],
      ]);

      await expect(
        simpleVoting.read.getUserVote([0n, addr1.account.address])
      ).to.be.rejectedWith("User has not voted");
    });

    it("Should handle hasUserVoted for non-existent poll", async function () {
      await expect(
        simpleVoting.read.hasUserVoted([999n, addr1.account.address])
      ).to.be.rejectedWith("Poll does not exist");
    });
  });
});
