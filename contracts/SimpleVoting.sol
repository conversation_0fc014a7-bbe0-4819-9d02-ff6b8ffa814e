// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * @title SimpleVoting
 * @dev A simple voting contract for creating polls and casting votes
 */
contract SimpleVoting {
    struct Poll {
        string question;
        string[] options;
        uint256[] votes;
        mapping(address => bool) hasVoted;
        mapping(address => uint256) voterChoice;
        bool isActive;
        address creator;
        uint256 createdAt;
    }

    Poll[] public polls;
    uint256 public pollCount;
    
    // Optional: mapping from creator to their poll IDs
    mapping(address => uint256[]) public creatorPolls;

    // Events
    event PollCreated(
        uint256 indexed pollId,
        address indexed creator,
        string question,
        uint256 optionCount
    );

    event VoteCast(
        uint256 indexed pollId,
        address indexed voter,
        uint256 optionIndex
    );

    event PollStatusChanged(
        uint256 indexed pollId,
        bool isActive
    );

    // Modifiers
    modifier pollExists(uint256 _pollId) {
        require(_pollId < polls.length, "Poll does not exist");
        _;
    }

    modifier pollActive(uint256 _pollId) {
        require(polls[_pollId].isActive, "Poll is not active");
        _;
    }

    modifier hasNotVoted(uint256 _pollId) {
        require(!polls[_pollId].hasVoted[msg.sender], "Already voted");
        _;
    }

    modifier validOption(uint256 _pollId, uint256 _optionIndex) {
        require(_optionIndex < polls[_pollId].options.length, "Invalid option");
        _;
    }

    modifier validPollData(string memory _question, string[] memory _options) {
        require(bytes(_question).length > 0, "Question cannot be empty");
        require(_options.length >= 2, "At least 2 options required");
        require(_options.length <= 10, "Too many options");

        for (uint i = 0; i < _options.length; i++) {
            require(bytes(_options[i]).length > 0, "Option cannot be empty");
        }
        _;
    }

    /**
     * @dev Create a new poll
     * @param _question The poll question
     * @param _options Array of voting options
     * @return pollId The ID of the created poll
     */
    function createPoll(
        string memory _question,
        string[] memory _options
    ) external validPollData(_question, _options) returns (uint256 pollId) {
        pollId = polls.length;
        
        // Create new poll
        polls.push();
        Poll storage newPoll = polls[pollId];
        
        newPoll.question = _question;
        newPoll.options = _options;
        newPoll.votes = new uint256[](_options.length);
        newPoll.isActive = true;
        newPoll.creator = msg.sender;
        newPoll.createdAt = block.timestamp;
        
        // Update mappings
        pollCount++;
        creatorPolls[msg.sender].push(pollId);
        
        emit PollCreated(pollId, msg.sender, _question, _options.length);
    }

    /**
     * @dev Cast a vote on a poll
     * @param _pollId The poll ID
     * @param _optionIndex The index of the chosen option
     */
    function vote(
        uint256 _pollId,
        uint256 _optionIndex
    ) 
        external 
        pollExists(_pollId) 
        pollActive(_pollId) 
        hasNotVoted(_pollId) 
        validOption(_pollId, _optionIndex) 
    {
        Poll storage poll = polls[_pollId];
        
        poll.hasVoted[msg.sender] = true;
        poll.voterChoice[msg.sender] = _optionIndex;
        poll.votes[_optionIndex]++;
        
        emit VoteCast(_pollId, msg.sender, _optionIndex);
    }

    /**
     * @dev Get poll information
     * @param _pollId The poll ID
     * @return question The poll question
     * @return options Array of voting options
     * @return votes Array of vote counts for each option
     * @return isActive Whether the poll is active
     * @return creator The poll creator address
     * @return createdAt The poll creation timestamp
     */
    function getPoll(uint256 _pollId)
        external
        view
        pollExists(_pollId)
        returns (
            string memory question,
            string[] memory options,
            uint256[] memory votes,
            bool isActive,
            address creator,
            uint256 createdAt
        )
    {
        Poll storage poll = polls[_pollId];
        return (
            poll.question,
            poll.options,
            poll.votes,
            poll.isActive,
            poll.creator,
            poll.createdAt
        );
    }

    /**
     * @dev Get the total number of polls
     * @return The total poll count
     */
    function getPollCount() external view returns (uint256) {
        return polls.length;
    }

    /**
     * @dev Check if a user has voted on a poll
     * @param _pollId The poll ID
     * @param _user The user address
     * @return Whether the user has voted
     */
    function hasUserVoted(
        uint256 _pollId,
        address _user
    ) external view pollExists(_pollId) returns (bool) {
        return polls[_pollId].hasVoted[_user];
    }

    /**
     * @dev Get a user's vote choice
     * @param _pollId The poll ID
     * @param _user The user address
     * @return The user's vote choice (option index)
     */
    function getUserVote(
        uint256 _pollId,
        address _user
    ) external view pollExists(_pollId) returns (uint256) {
        require(polls[_pollId].hasVoted[_user], "User has not voted");
        return polls[_pollId].voterChoice[_user];
    }

    /**
     * @dev Toggle poll active status (only creator can call)
     * @param _pollId The poll ID
     */
    function togglePollStatus(uint256 _pollId) 
        external 
        pollExists(_pollId) 
    {
        require(polls[_pollId].creator == msg.sender, "Only creator can toggle status");
        
        polls[_pollId].isActive = !polls[_pollId].isActive;
        emit PollStatusChanged(_pollId, polls[_pollId].isActive);
    }

    /**
     * @dev Get polls created by a specific address
     * @param _creator The creator address
     * @return Array of poll IDs created by the address
     */
    function getCreatorPolls(address _creator) external view returns (uint256[] memory) {
        return creatorPolls[_creator];
    }
}
