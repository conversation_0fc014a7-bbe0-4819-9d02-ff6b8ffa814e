{"_format": "hh-sol-cache-2", "files": {"/Users/<USER>/Documents/GitHub/unsw/comp6452-project2/contracts/SimpleVoting.sol": {"lastModificationDate": 1749874139920, "contentHash": "832ab02cc9c9a07770c5aa8515e41b74", "sourceName": "contracts/SimpleVoting.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.19"], "artifacts": ["SimpleVoting"]}}}