"use client";

import { CreatePollForm } from "@/components/forms/CreatePollForm";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { PollList } from "@/components/voting/PollList";
import { ConnectWallet } from "@/components/wallet/ConnectWallet";
import { useState } from "react";

export default function Home() {
  const [showCreateForm, setShowCreateForm] = useState(false);

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-card shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-card-foreground">
                Voting DApp
              </h1>
              <p className="text-muted-foreground">
                Decentralized voting platform on Base Sepolia
              </p>
            </div>
            <ConnectWallet />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Welcome Section */}
          <Card className="bg-card border-border shadow-md">
            <CardHeader>
              <CardTitle className="text-card-foreground">
                Welcome to the Voting DApp
              </CardTitle>
              <CardDescription className="text-muted-foreground">
                Create polls, vote on proposals, and participate in
                decentralized governance. Connect your wallet to get started.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4">
                <Button
                  onClick={() => setShowCreateForm(!showCreateForm)}
                  className="bg-primary text-primary-foreground hover:bg-primary/90"
                >
                  {showCreateForm ? "Hide Form" : "Create New Poll"}
                </Button>
                <Button
                  variant="outline"
                  onClick={() =>
                    window.scrollTo({ top: 400, behavior: "smooth" })
                  }
                  className="border-border text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                >
                  View Polls
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Create Poll Form */}
          {showCreateForm && (
            <CreatePollForm
              onSuccess={() => {
                setShowCreateForm(false);
                // Scroll to polls section
                setTimeout(() => {
                  window.scrollTo({ top: 600, behavior: "smooth" });
                }, 1000);
              }}
            />
          )}

          {/* Polls List */}
          <PollList />
        </div>
      </main>
      {/* Footer */}
      <footer className="border-t border-border bg-card mt-16">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center text-muted-foreground">
            <p className="mb-2">Built with Next.js 15, Wagmi, and RainbowKit</p>
            <p className="text-sm">Deployed on Base Sepolia Testnet</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
