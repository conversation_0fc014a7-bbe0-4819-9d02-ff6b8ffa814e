// TypeScript interfaces for poll data
export interface PollData {
  id: bigint;
  question: string;
  options: string[];
  votes: bigint[];
  isActive: boolean;
  creator: `0x${string}`;
  createdAt: bigint;
  totalVotes: bigint;
}

export interface UserVoteStatus {
  hasVoted: boolean;
  choice?: number;
}

export interface CreatePollForm {
  question: string;
  options: string[];
}

export interface VoteForm {
  pollId: bigint;
  optionIndex: number;
}
