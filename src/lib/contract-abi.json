[{"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "pollId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "creator", "type": "address"}, {"indexed": false, "internalType": "string", "name": "question", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "optionCount", "type": "uint256"}], "name": "PollCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "pollId", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "isActive", "type": "bool"}], "name": "PollStatusChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "pollId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "voter", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "optionIndex", "type": "uint256"}], "name": "VoteCast", "type": "event"}, {"inputs": [{"internalType": "string", "name": "_question", "type": "string"}, {"internalType": "string[]", "name": "_options", "type": "string[]"}], "name": "createPoll", "outputs": [{"internalType": "uint256", "name": "pollId", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "creatorP<PERSON>s", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_creator", "type": "address"}], "name": "getCreatorPolls", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_pollId", "type": "uint256"}], "name": "getPoll", "outputs": [{"internalType": "string", "name": "question", "type": "string"}, {"internalType": "string[]", "name": "options", "type": "string[]"}, {"internalType": "uint256[]", "name": "votes", "type": "uint256[]"}, {"internalType": "bool", "name": "isActive", "type": "bool"}, {"internalType": "address", "name": "creator", "type": "address"}, {"internalType": "uint256", "name": "createdAt", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getPollCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_pollId", "type": "uint256"}, {"internalType": "address", "name": "_user", "type": "address"}], "name": "getUserVote", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_pollId", "type": "uint256"}, {"internalType": "address", "name": "_user", "type": "address"}], "name": "hasUserVoted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pollCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "polls", "outputs": [{"internalType": "string", "name": "question", "type": "string"}, {"internalType": "bool", "name": "isActive", "type": "bool"}, {"internalType": "address", "name": "creator", "type": "address"}, {"internalType": "uint256", "name": "createdAt", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_pollId", "type": "uint256"}], "name": "togglePollStatus", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_pollId", "type": "uint256"}, {"internalType": "uint256", "name": "_optionIndex", "type": "uint256"}], "name": "vote", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]