import contractConfig from "@/lib/contract-config.json";
import { PollData, UserVoteStatus } from "@/lib/types";
import { CONTRACT_ADDRESS } from "@/lib/wagmi";
import {
  useReadContract,
  useWatchContractEvent,
  useWriteContract,
} from "wagmi";

const CONTRACT_ABI = contractConfig.abi;

// Read hooks
export function usePollCount() {
  return useReadContract({
    address: CONTRACT_ADDRESS,
    abi: CONTRACT_ABI,
    functionName: "getPollCount",
  });
}

export function usePoll(pollId: bigint) {
  return useReadContract({
    address: CONTRACT_ADDRESS,
    abi: CONTRACT_ABI,
    functionName: "getPoll",
    args: [pollId],
    query: {
      enabled: !!pollId,
    },
  });
}

export function useUserVoteStatus(
  pollId: bigint,
  userAddress: `0x${string}` | undefined
) {
  const { data: hasVoted } = useReadContract({
    address: CONTRACT_ADDRESS,
    abi: CONTRACT_ABI,
    functionName: "hasUserVoted",
    args: [pollId, userAddress!],
    query: {
      enabled: !!pollId && !!userAddress,
    },
  });

  const { data: userChoice } = useReadContract({
    address: CONTRACT_ADDRESS,
    abi: CONTRACT_ABI,
    functionName: "getUserVote",
    args: [pollId, userAddress!],
    query: {
      enabled: !!pollId && !!userAddress && !!hasVoted,
    },
  });

  return {
    hasVoted: !!hasVoted,
    choice: userChoice ? Number(userChoice) : undefined,
  } as UserVoteStatus;
}

export function useCreatorPolls(creatorAddress: `0x${string}` | undefined) {
  return useReadContract({
    address: CONTRACT_ADDRESS,
    abi: CONTRACT_ABI,
    functionName: "getCreatorPolls",
    args: [creatorAddress!],
    query: {
      enabled: !!creatorAddress,
    },
  });
}

// Write hooks
export function useCreatePoll() {
  return useWriteContract();
}

export function useVote() {
  return useWriteContract();
}

export function useTogglePollStatus() {
  return useWriteContract();
}

// Event watching hooks
export function useWatchPollCreated(onPollCreated?: (logs: unknown[]) => void) {
  return useWatchContractEvent({
    address: CONTRACT_ADDRESS,
    abi: CONTRACT_ABI,
    eventName: "PollCreated",
    onLogs: onPollCreated,
  });
}

export function useWatchVoteCast(
  pollId?: bigint,
  onVoteCast?: (logs: unknown[]) => void
) {
  return useWatchContractEvent({
    address: CONTRACT_ADDRESS,
    abi: CONTRACT_ABI,
    eventName: "VoteCast",
    args: pollId ? { pollId } : undefined,
    onLogs: onVoteCast,
  });
}

// Helper functions
export function createPollArgs(question: string, options: string[]) {
  return {
    address: CONTRACT_ADDRESS,
    abi: CONTRACT_ABI,
    functionName: "createPoll",
    args: [question, options],
  };
}

export function voteArgs(pollId: bigint, optionIndex: bigint) {
  return {
    address: CONTRACT_ADDRESS,
    abi: CONTRACT_ABI,
    functionName: "vote",
    args: [pollId, optionIndex],
  };
}

export function togglePollStatusArgs(pollId: bigint) {
  return {
    address: CONTRACT_ADDRESS,
    abi: CONTRACT_ABI,
    functionName: "togglePollStatus",
    args: [pollId],
  };
}

// Transform poll data helper
export function transformPollData(pollData: unknown, pollId: bigint): PollData {
  if (!pollData || !Array.isArray(pollData)) return {} as PollData;

  const [question, options, votes, isActive, creator, createdAt] = pollData as [
    string,
    string[],
    bigint[],
    boolean,
    `0x${string}`,
    bigint,
  ];
  const totalVotes = votes.reduce(
    (sum: bigint, vote: bigint) => sum + vote,
    0n
  );

  return {
    id: pollId,
    question,
    options,
    votes,
    isActive,
    creator,
    createdAt,
    totalVotes,
  };
}
